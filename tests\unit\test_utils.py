"""
Unit tests for the utils module.

This module contains tests for utility functions and helpers.
"""

import pytest
import os
import tempfile
import datetime
import logging
from unittest.mock import patch, mock_open

from exchange_log_analyzer.utils.helpers import (
    setup_logging, format_datetime, validate_file_path, validate_directory_path,
    get_file_size_mb, convert_timezone, sanitize_filename, format_file_size,
    truncate_string
)
from exchange_log_analyzer.utils.constants import (
    SUPPORTED_TIMEZONES, DEFAULT_DATE_FORMAT, FIELD_TRANSLATIONS,
    EVENT_ID_DESCRIPTIONS
)


class TestHelperFunctions:
    """Test cases for helper functions."""
    
    def test_setup_logging_console_only(self):
        """Test setup_logging with console handler only."""
        setup_logging(level=logging.DEBUG)
        
        root_logger = logging.getLogger()
        assert root_logger.level == logging.DEBUG
        
        # Should have at least one handler (console)
        assert len(root_logger.handlers) >= 1
    
    def test_setup_logging_with_file(self, temp_directory):
        """Test setup_logging with file handler."""
        log_file = os.path.join(temp_directory, "test.log")
        setup_logging(level=logging.INFO, log_file=log_file)
        
        root_logger = logging.getLogger()
        assert root_logger.level == logging.INFO
        
        # Should have both console and file handlers
        assert len(root_logger.handlers) >= 2
        
        # Test that file is created
        root_logger.info("Test message")
        assert os.path.exists(log_file)
    
    def test_format_datetime_with_datetime_object(self):
        """Test format_datetime with datetime object."""
        dt = datetime.datetime(2025, 1, 20, 8, 30, 15)
        result = format_datetime(dt)
        
        assert result == "2025-01-20 08:30:15"
    
    def test_format_datetime_with_custom_format(self):
        """Test format_datetime with custom format."""
        dt = datetime.datetime(2025, 1, 20, 8, 30, 15)
        result = format_datetime(dt, "%Y/%m/%d %H:%M")
        
        assert result == "2025/01/20 08:30"
    
    def test_format_datetime_with_iso_string(self):
        """Test format_datetime with ISO string."""
        iso_string = "2025-01-20T08:30:15Z"
        result = format_datetime(iso_string)
        
        assert "2025-01-20" in result
        assert "08:30:15" in result
    
    def test_format_datetime_with_invalid_string(self):
        """Test format_datetime with invalid string."""
        invalid_string = "not a date"
        result = format_datetime(invalid_string)
        
        assert result == invalid_string  # Should return original
    
    def test_validate_file_path_existing_file(self, temp_directory):
        """Test validate_file_path with existing file."""
        test_file = os.path.join(temp_directory, "test.txt")
        with open(test_file, 'w') as f:
            f.write("test content")
        
        assert validate_file_path(test_file) is True
    
    def test_validate_file_path_nonexistent_file(self):
        """Test validate_file_path with non-existent file."""
        assert validate_file_path("/nonexistent/file.txt") is False
    
    def test_validate_file_path_directory(self, temp_directory):
        """Test validate_file_path with directory path."""
        assert validate_file_path(temp_directory) is False
    
    def test_validate_directory_path_existing_directory(self, temp_directory):
        """Test validate_directory_path with existing directory."""
        assert validate_directory_path(temp_directory) is True
    
    def test_validate_directory_path_nonexistent_directory(self):
        """Test validate_directory_path with non-existent directory."""
        assert validate_directory_path("/nonexistent/directory") is False
    
    def test_validate_directory_path_file(self, temp_directory):
        """Test validate_directory_path with file path."""
        test_file = os.path.join(temp_directory, "test.txt")
        with open(test_file, 'w') as f:
            f.write("test content")
        
        assert validate_directory_path(test_file) is False
    
    def test_get_file_size_mb_existing_file(self, temp_directory):
        """Test get_file_size_mb with existing file."""
        test_file = os.path.join(temp_directory, "test.txt")
        test_content = "x" * 1024  # 1KB content
        with open(test_file, 'w') as f:
            f.write(test_content)
        
        size_mb = get_file_size_mb(test_file)
        assert isinstance(size_mb, float)
        assert size_mb > 0
        assert size_mb < 1  # Should be less than 1MB
    
    def test_get_file_size_mb_nonexistent_file(self):
        """Test get_file_size_mb with non-existent file."""
        size_mb = get_file_size_mb("/nonexistent/file.txt")
        assert size_mb == 0.0
    
    def test_convert_timezone_utc_to_taipei(self):
        """Test convert_timezone from UTC to Taipei."""
        import pytz
        
        utc_dt = datetime.datetime(2025, 1, 20, 8, 30, 15, tzinfo=pytz.UTC)
        result = convert_timezone(utc_dt, 'Asia/Taipei')
        
        assert result.tzinfo is not None
        assert result.hour != utc_dt.hour  # Should be different due to timezone
    
    def test_convert_timezone_naive_datetime(self):
        """Test convert_timezone with naive datetime."""
        naive_dt = datetime.datetime(2025, 1, 20, 8, 30, 15)
        result = convert_timezone(naive_dt, 'Asia/Taipei')
        
        assert result.tzinfo is not None
    
    def test_convert_timezone_invalid_timezone(self):
        """Test convert_timezone with invalid timezone."""
        import pytz
        
        utc_dt = datetime.datetime(2025, 1, 20, 8, 30, 15, tzinfo=pytz.UTC)
        result = convert_timezone(utc_dt, 'Invalid/Timezone')
        
        # Should return original datetime on error
        assert result == utc_dt
    
    def test_sanitize_filename_with_invalid_chars(self):
        """Test sanitize_filename with invalid characters."""
        filename = 'test<>:"/\\|?*.txt'
        result = sanitize_filename(filename)
        
        assert '<' not in result
        assert '>' not in result
        assert ':' not in result
        assert '"' not in result
        assert '/' not in result
        assert '\\' not in result
        assert '|' not in result
        assert '?' not in result
        assert '*' not in result
    
    def test_sanitize_filename_with_spaces(self):
        """Test sanitize_filename with leading/trailing spaces."""
        filename = '  test file.txt  '
        result = sanitize_filename(filename)
        
        assert not result.startswith(' ')
        assert not result.endswith(' ')
        assert 'test file.txt' in result
    
    def test_sanitize_filename_empty_string(self):
        """Test sanitize_filename with empty string."""
        result = sanitize_filename('')
        assert result == 'untitled'
    
    def test_sanitize_filename_only_invalid_chars(self):
        """Test sanitize_filename with only invalid characters."""
        result = sanitize_filename('<>:"/\\|?*')
        assert result == 'untitled'
    
    def test_format_file_size_bytes(self):
        """Test format_file_size with bytes."""
        assert format_file_size(0) == "0 B"
        assert format_file_size(512) == "512.0 B"
        assert format_file_size(1023) == "1023.0 B"
    
    def test_format_file_size_kilobytes(self):
        """Test format_file_size with kilobytes."""
        assert format_file_size(1024) == "1.0 KB"
        assert format_file_size(1536) == "1.5 KB"
    
    def test_format_file_size_megabytes(self):
        """Test format_file_size with megabytes."""
        assert format_file_size(1024 * 1024) == "1.0 MB"
        assert format_file_size(1024 * 1024 * 2.5) == "2.5 MB"
    
    def test_format_file_size_gigabytes(self):
        """Test format_file_size with gigabytes."""
        assert format_file_size(1024 * 1024 * 1024) == "1.0 GB"
    
    def test_truncate_string_short_text(self):
        """Test truncate_string with text shorter than max length."""
        text = "Short text"
        result = truncate_string(text, max_length=50)
        
        assert result == text
    
    def test_truncate_string_long_text(self):
        """Test truncate_string with text longer than max length."""
        text = "This is a very long text that should be truncated"
        result = truncate_string(text, max_length=20)
        
        assert len(result) == 20
        assert result.endswith("...")
        assert "This is a very" in result
    
    def test_truncate_string_custom_suffix(self):
        """Test truncate_string with custom suffix."""
        text = "This is a long text"
        result = truncate_string(text, max_length=15, suffix="[...]")
        
        assert len(result) == 15
        assert result.endswith("[...]")


class TestConstants:
    """Test cases for constants."""
    
    def test_supported_timezones_list(self):
        """Test that SUPPORTED_TIMEZONES is a valid list."""
        assert isinstance(SUPPORTED_TIMEZONES, list)
        assert len(SUPPORTED_TIMEZONES) > 0
        assert 'UTC' in SUPPORTED_TIMEZONES
        assert 'Asia/Taipei' in SUPPORTED_TIMEZONES
    
    def test_default_date_format(self):
        """Test that DEFAULT_DATE_FORMAT is valid."""
        assert isinstance(DEFAULT_DATE_FORMAT, str)
        assert '%Y' in DEFAULT_DATE_FORMAT
        assert '%m' in DEFAULT_DATE_FORMAT
        assert '%d' in DEFAULT_DATE_FORMAT
    
    def test_field_translations_dict(self):
        """Test that FIELD_TRANSLATIONS is a valid dictionary."""
        assert isinstance(FIELD_TRANSLATIONS, dict)
        assert len(FIELD_TRANSLATIONS) > 0
        assert 'date-time' in FIELD_TRANSLATIONS
        assert 'event-id' in FIELD_TRANSLATIONS
        
        # Check that all values are strings
        for key, value in FIELD_TRANSLATIONS.items():
            assert isinstance(key, str)
            assert isinstance(value, str)
    
    def test_event_id_descriptions_dict(self):
        """Test that EVENT_ID_DESCRIPTIONS is a valid dictionary."""
        assert isinstance(EVENT_ID_DESCRIPTIONS, dict)
        assert len(EVENT_ID_DESCRIPTIONS) > 0
        assert 'RECEIVE' in EVENT_ID_DESCRIPTIONS
        assert 'SEND' in EVENT_ID_DESCRIPTIONS
        
        # Check that all values are strings
        for key, value in EVENT_ID_DESCRIPTIONS.items():
            assert isinstance(key, str)
            assert isinstance(value, str)
