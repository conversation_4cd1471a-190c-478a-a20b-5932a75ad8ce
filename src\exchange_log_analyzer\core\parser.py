"""
Log parsing functionality for Exchange message tracking logs.

This module provides functions to parse Exchange Server message tracking logs
and filter them based on various criteria.
"""

import pandas as pd
import pytz
from typing import Optional, Union
import logging

# Set up logging
logger = logging.getLogger(__name__)


class LogParser:
    """A class for parsing Exchange message tracking logs."""
    
    def __init__(self, timezone: str = 'UTC'):
        """
        Initialize the LogParser.
        
        Args:
            timezone (str): The timezone to convert timestamps to. Defaults to 'UTC'.
        """
        self.timezone = timezone
    
    def parse_file(self, file_path: str) -> pd.DataFrame:
        """
        Parse a single log file.
        
        Args:
            file_path (str): Path to the log file.
            
        Returns:
            pd.DataFrame: Parsed log data.
        """
        return parse_log(file_path, self.timezone)
    
    def filter_data(self, df: pd.DataFrame, keyword: Optional[str] = None, 
                   start_date: Optional[str] = None, 
                   end_date: Optional[str] = None) -> pd.DataFrame:
        """
        Filter log data based on criteria.
        
        Args:
            df (pd.DataFrame): The log data to filter.
            keyword (str, optional): Keyword to search for.
            start_date (str, optional): Start date for filtering.
            end_date (str, optional): End date for filtering.
            
        Returns:
            pd.DataFrame: Filtered log data.
        """
        return filter_logs(df, keyword, start_date, end_date)


def parse_log(file_path: str, timezone: str = 'UTC') -> pd.DataFrame:
    """
    Parse an Exchange message tracking log file.
    
    Args:
        file_path (str): Path to the log file.
        timezone (str): Timezone to convert timestamps to.
        
    Returns:
        pd.DataFrame: Parsed log data with proper column headers and timestamps.
    """
    try:
        # Read non-comment lines
        with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
            lines = [line.strip() for line in f if not line.startswith("#")]

        # Extract headers from the #Fields line
        with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
            fields_line = next(line for line in f if line.startswith("#Fields:"))
            headers = fields_line.replace("#Fields: ", "").strip().split(",")

        # Parse data
        data = [line.split(",", len(headers) - 1) for line in lines]
        df = pd.DataFrame(data, columns=headers)

        # Convert datetime column if present
        if 'date-time' in df.columns:
            df['date-time'] = pd.to_datetime(df['date-time'], errors='coerce', utc=True)
            if timezone and timezone != 'UTC':
                try:
                    df['date-time'] = df['date-time'].dt.tz_convert(timezone)
                except Exception as e:
                    logger.warning(f"Failed to convert timezone to {timezone}: {e}")

        logger.info(f"Successfully parsed {len(df)} records from {file_path}")
        return df

    except Exception as e:
        logger.error(f"Failed to parse log file: {file_path}. Error: {e}")
        return pd.DataFrame()


def filter_logs(df: pd.DataFrame, keyword: Optional[str] = None, 
               start_date: Optional[Union[str, pd.Timestamp]] = None, 
               end_date: Optional[Union[str, pd.Timestamp]] = None) -> pd.DataFrame:
    """
    Filter log data based on keyword and date range.
    
    Args:
        df (pd.DataFrame): The log data to filter.
        keyword (str, optional): Keyword to search for across all columns.
        start_date (str or pd.Timestamp, optional): Start date for filtering.
        end_date (str or pd.Timestamp, optional): End date for filtering.
        
    Returns:
        pd.DataFrame: Filtered log data.
    """
    filtered_df = df.copy()
    
    # Filter by keyword
    if keyword:
        mask = filtered_df.apply(
            lambda row: row.astype(str).str.contains(
                keyword, case=False, na=False
            ).any(), axis=1
        )
        filtered_df = filtered_df[mask]
        logger.info(f"Keyword filter '{keyword}' reduced data to {len(filtered_df)} records")

    # Filter by date range
    if 'date-time' in filtered_df.columns:
        if start_date:
            start_ts = pd.to_datetime(start_date)
            if filtered_df['date-time'].dt.tz is not None:
                start_ts = start_ts.tz_localize(filtered_df['date-time'].dt.tz)
            filtered_df = filtered_df[filtered_df['date-time'] >= start_ts]
            logger.info(f"Start date filter reduced data to {len(filtered_df)} records")
            
        if end_date:
            end_ts = pd.to_datetime(end_date)
            if filtered_df['date-time'].dt.tz is not None:
                end_ts = end_ts.tz_localize(filtered_df['date-time'].dt.tz)
            filtered_df = filtered_df[filtered_df['date-time'] <= end_ts]
            logger.info(f"End date filter reduced data to {len(filtered_df)} records")

    return filtered_df
