#!/usr/bin/env python3
"""
測試執行腳本

這個腳本提供了便利的方式來執行各種測試和程式碼品質檢查。
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def run_command(command, description):
    """執行命令並顯示結果"""
    print(f"\n{'='*60}")
    print(f"執行: {description}")
    print(f"命令: {' '.join(command)}")
    print('='*60)
    
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("警告:")
            print(result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"錯誤: 命令執行失敗 (返回碼: {e.returncode})")
        print("標準輸出:")
        print(e.stdout)
        print("錯誤輸出:")
        print(e.stderr)
        return False
    except FileNotFoundError:
        print(f"錯誤: 找不到命令 '{command[0]}'")
        print("請確認相關工具已安裝")
        return False


def check_dependencies():
    """檢查必要的依賴是否已安裝"""
    dependencies = [
        ('pytest', 'pytest --version'),
        ('black', 'black --version'),
        ('flake8', 'flake8 --version'),
        ('mypy', 'mypy --version')
    ]
    
    print("檢查依賴套件...")
    missing = []
    
    for name, command in dependencies:
        try:
            subprocess.run(command.split(), check=True, capture_output=True)
            print(f"✓ {name} 已安裝")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"✗ {name} 未安裝")
            missing.append(name)
    
    if missing:
        print(f"\n缺少以下套件: {', '.join(missing)}")
        print("請執行: pip install -e .[dev]")
        return False
    
    return True


def run_unit_tests():
    """執行單元測試"""
    return run_command(
        ['pytest', 'tests/unit/', '-v', '--tb=short'],
        "單元測試"
    )


def run_integration_tests():
    """執行整合測試"""
    return run_command(
        ['pytest', 'tests/integration/', '-v', '--tb=short'],
        "整合測試"
    )


def run_all_tests():
    """執行所有測試"""
    return run_command(
        ['pytest', 'tests/', '-v', '--tb=short'],
        "所有測試"
    )


def run_tests_with_coverage():
    """執行測試並生成覆蓋率報告"""
    success = run_command(
        ['pytest', 'tests/', '--cov=src/exchange_log_analyzer', '--cov-report=term-missing', '--cov-report=html'],
        "測試覆蓋率分析"
    )
    
    if success:
        print("\n覆蓋率報告已生成到 htmlcov/ 目錄")
        print("開啟 htmlcov/index.html 查看詳細報告")
    
    return success


def run_code_formatting():
    """執行程式碼格式化"""
    return run_command(
        ['black', 'src/', 'tests/', 'examples/', 'scripts/'],
        "程式碼格式化 (Black)"
    )


def run_code_linting():
    """執行程式碼檢查"""
    return run_command(
        ['flake8', 'src/', 'tests/'],
        "程式碼風格檢查 (Flake8)"
    )


def run_type_checking():
    """執行型別檢查"""
    return run_command(
        ['mypy', 'src/exchange_log_analyzer/'],
        "型別檢查 (MyPy)"
    )


def run_security_check():
    """執行安全性檢查"""
    try:
        import bandit
        return run_command(
            ['bandit', '-r', 'src/', '-f', 'json'],
            "安全性檢查 (Bandit)"
        )
    except ImportError:
        print("Bandit 未安裝，跳過安全性檢查")
        print("安裝: pip install bandit")
        return True


def run_performance_tests():
    """執行效能測試"""
    return run_command(
        ['pytest', 'tests/', '-m', 'slow', '-v'],
        "效能測試"
    )


def run_gui_tests():
    """執行 GUI 測試"""
    return run_command(
        ['pytest', 'tests/', '-m', 'gui', '-v'],
        "GUI 測試"
    )


def run_quick_check():
    """執行快速檢查（不包含慢速測試）"""
    print("執行快速檢查...")
    
    checks = [
        ("程式碼格式化", run_code_formatting),
        ("程式碼風格檢查", run_code_linting),
        ("快速測試", lambda: run_command(['pytest', 'tests/', '-m', 'not slow', '-v'], "快速測試")),
    ]
    
    results = []
    for name, func in checks:
        print(f"\n開始 {name}...")
        success = func()
        results.append((name, success))
    
    print(f"\n{'='*60}")
    print("快速檢查結果:")
    print('='*60)
    
    all_passed = True
    for name, success in results:
        status = "✓ 通過" if success else "✗ 失敗"
        print(f"{name}: {status}")
        if not success:
            all_passed = False
    
    return all_passed


def run_full_check():
    """執行完整檢查"""
    print("執行完整檢查...")
    
    checks = [
        ("依賴檢查", check_dependencies),
        ("程式碼格式化", run_code_formatting),
        ("程式碼風格檢查", run_code_linting),
        ("型別檢查", run_type_checking),
        ("安全性檢查", run_security_check),
        ("所有測試", run_all_tests),
        ("測試覆蓋率", run_tests_with_coverage),
    ]
    
    results = []
    for name, func in checks:
        print(f"\n開始 {name}...")
        success = func()
        results.append((name, success))
        
        if not success and name == "依賴檢查":
            print("依賴檢查失敗，停止後續檢查")
            break
    
    print(f"\n{'='*60}")
    print("完整檢查結果:")
    print('='*60)
    
    all_passed = True
    for name, success in results:
        status = "✓ 通過" if success else "✗ 失敗"
        print(f"{name}: {status}")
        if not success:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有檢查都通過了！")
    else:
        print("\n❌ 有些檢查失敗了，請修復後重新執行")
    
    return all_passed


def main():
    """主函式"""
    parser = argparse.ArgumentParser(description="Exchange Log Analyzer 測試執行腳本")
    parser.add_argument('action', nargs='?', default='quick',
                       choices=['quick', 'full', 'unit', 'integration', 'all', 'coverage', 
                               'format', 'lint', 'type', 'security', 'performance', 'gui'],
                       help='要執行的操作')
    
    args = parser.parse_args()
    
    # 確保在專案根目錄執行
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    os.chdir(project_root)
    
    print(f"Exchange Log Analyzer 測試執行腳本")
    print(f"工作目錄: {os.getcwd()}")
    
    action_map = {
        'quick': run_quick_check,
        'full': run_full_check,
        'unit': run_unit_tests,
        'integration': run_integration_tests,
        'all': run_all_tests,
        'coverage': run_tests_with_coverage,
        'format': run_code_formatting,
        'lint': run_code_linting,
        'type': run_type_checking,
        'security': run_security_check,
        'performance': run_performance_tests,
        'gui': run_gui_tests,
    }
    
    action_func = action_map.get(args.action)
    if action_func:
        success = action_func()
        sys.exit(0 if success else 1)
    else:
        print(f"未知的操作: {args.action}")
        sys.exit(1)


if __name__ == '__main__':
    main()
