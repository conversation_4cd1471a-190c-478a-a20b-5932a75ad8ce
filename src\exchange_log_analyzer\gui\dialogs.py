"""
Dialog windows for Exchange Log Analyzer.

This module contains various dialog windows used in the application.
"""

import os
import tempfile
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QListWidget, QMessageBox, QProgressDialog, QDialogButtonBox
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
import logging

logger = logging.getLogger(__name__)

# Optional SMB imports
try:
    from smbprotocol.connection import Connection
    from smbprotocol.session import Session
    from smbprotocol.tree import TreeConnect
    from smbprotocol.open import Open, CreateDisposition, ShareAccess, FilePipePrinterAccessMask
    from smbprotocol.exceptions import SMBAuthenticationError
    SMB_AVAILABLE = True
except ImportError:
    logger.warning("SMB libraries not available. SMB functionality will be disabled.")
    SMB_AVAILABLE = False

try:
    from smb.SMBConnection import SMBConnection
    PYSMB_AVAILABLE = True
except ImportError:
    logger.warning("pysmb library not available. Alternative SMB functionality will be disabled.")
    PYSMB_AVAILABLE = False


class SmbDownloadDialog(QDialog):
    """Dialog for downloading log files via SMB protocol."""
    
    def __init__(self, parent=None):
        """Initialize the SMB download dialog."""
        super().__init__(parent)
        self.setWindowTitle("連線至 Exchange - SMB 模式")
        self.setMinimumWidth(650)
        self.files = []
        self.conn = None
        self.server = ''
        self.share = 'LogsMessageTracking'
        
        if not (SMB_AVAILABLE or PYSMB_AVAILABLE):
            QMessageBox.warning(
                self, "功能不可用", 
                "SMB 功能需要安裝額外的套件:\n"
                "pip install smbprotocol pysmb\n\n"
                "請安裝後重新啟動應用程式。"
            )
            self.reject()
            return
            
        self.init_ui()
        
    def init_ui(self):
        """Initialize the user interface."""
        layout = QVBoxLayout()
        
        # Connection form
        form_layout = QHBoxLayout()
        
        self.server_input = QLineEdit()
        self.server_input.setPlaceholderText("伺服器名稱或IP")
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("帳號")
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("密碼")
        self.password_input.setEchoMode(QLineEdit.Password)
        
        form_layout.addWidget(QLabel("伺服器:"))
        form_layout.addWidget(self.server_input)
        form_layout.addWidget(QLabel("帳號:"))
        form_layout.addWidget(self.username_input)
        form_layout.addWidget(QLabel("密碼:"))
        form_layout.addWidget(self.password_input)
        
        # Connect button
        self.connect_button = QPushButton("連線")
        self.connect_button.clicked.connect(self.connect_to_server)
        
        # File list
        self.file_list = QListWidget()
        self.file_list.setSelectionMode(QListWidget.MultiSelection)
        
        # Download button
        self.download_button = QPushButton("下載選取的檔案")
        self.download_button.clicked.connect(self.download_files)
        self.download_button.setEnabled(False)
        
        # Button box
        button_box = QDialogButtonBox(QDialogButtonBox.Close)
        button_box.rejected.connect(self.reject)
        
        # Add to layout
        layout.addLayout(form_layout)
        layout.addWidget(self.connect_button)
        layout.addWidget(QLabel("可用的 LOG 檔案:"))
        layout.addWidget(self.file_list)
        layout.addWidget(self.download_button)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
        
    def connect_to_server(self):
        """Connect to the SMB server."""
        server = self.server_input.text().strip()
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not all([server, username, password]):
            QMessageBox.warning(self, "警告", "請填入所有連線資訊")
            return
            
        try:
            self.connect_button.setEnabled(False)
            self.connect_button.setText("連線中...")
            
            # Try to connect using available SMB library
            if PYSMB_AVAILABLE:
                self.connect_with_pysmb(server, username, password)
            elif SMB_AVAILABLE:
                self.connect_with_smbprotocol(server, username, password)
            else:
                raise Exception("沒有可用的 SMB 連線庫")
                
        except Exception as e:
            QMessageBox.critical(self, "連線錯誤", f"無法連線到伺服器:\n{str(e)}")
        finally:
            self.connect_button.setEnabled(True)
            self.connect_button.setText("連線")
            
    def connect_with_pysmb(self, server, username, password):
        """Connect using pysmb library."""
        if not PYSMB_AVAILABLE:
            raise Exception("pysmb 庫不可用")
            
        # Implementation would go here
        # This is a simplified version
        QMessageBox.information(self, "資訊", "pysmb 連線功能尚未完全實作")
        
    def connect_with_smbprotocol(self, server, username, password):
        """Connect using smbprotocol library."""
        if not SMB_AVAILABLE:
            raise Exception("smbprotocol 庫不可用")
            
        # Implementation would go here
        # This is a simplified version
        QMessageBox.information(self, "資訊", "smbprotocol 連線功能尚未完全實作")
        
    def list_files(self):
        """List available log files on the server."""
        # This would list files from the SMB share
        # For now, show a placeholder
        self.file_list.clear()
        self.file_list.addItem("範例檔案.log (功能開發中)")
        self.download_button.setEnabled(True)
        
    def download_files(self):
        """Download selected files from the server."""
        selected_items = self.file_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "警告", "請選擇要下載的檔案")
            return
            
        # For now, show a message that this is under development
        QMessageBox.information(
            self, "資訊", 
            "檔案下載功能正在開發中。\n"
            "目前請使用本機檔案載入功能。"
        )


class ProgressDialog(QProgressDialog):
    """Enhanced progress dialog with cancellation support."""
    
    def __init__(self, title="處理中", message="請稍候...", parent=None):
        """Initialize the progress dialog."""
        super().__init__(message, "取消", 0, 100, parent)
        self.setWindowTitle(title)
        self.setWindowModality(Qt.WindowModal)
        self.setMinimumDuration(0)
        
    def update_progress(self, value, message=None):
        """Update progress value and message."""
        self.setValue(value)
        if message:
            self.setLabelText(message)
