# 開發指南 (Development Guide)

本文檔提供 Exchange Log Analyzer 專案的詳細開發指南，包括架構設計、開發流程和最佳實踐。

## 📋 目錄

- [專案架構](#專案架構)
- [開發環境](#開發環境)
- [程式碼組織](#程式碼組織)
- [開發流程](#開發流程)
- [測試策略](#測試策略)
- [效能考量](#效能考量)
- [部署指南](#部署指南)

## 🏗 專案架構

### 整體架構

```
Exchange Log Analyzer
├── 核心層 (Core Layer)
│   ├── 日誌解析器 (Log Parser)
│   ├── 資料篩選器 (Data Filter)
│   └── 設定管理 (Configuration)
├── 服務層 (Service Layer)
│   ├── 檔案處理服務 (File Service)
│   ├── 資料處理服務 (Data Service)
│   └── 匯出服務 (Export Service)
├── 介面層 (UI Layer)
│   ├── 主視窗 (Main Window)
│   ├── 對話框 (Dialogs)
│   └── 工作執行緒 (Workers)
└── 工具層 (Utility Layer)
    ├── 輔助函式 (Helpers)
    └── 常數定義 (Constants)
```

### 模組依賴關係

```mermaid
graph TD
    A[GUI Layer] --> B[Core Layer]
    A --> C[Utils Layer]
    B --> C
    D[Workers] --> B
    D --> C
    E[Main App] --> A
    E --> B
```

### 設計原則

1. **單一職責原則**: 每個模組專注於特定功能
2. **開放封閉原則**: 對擴展開放，對修改封閉
3. **依賴反轉原則**: 高層模組不依賴低層模組
4. **介面隔離原則**: 使用小而專一的介面

## 🛠 開發環境

### 必要工具

```bash
# Python 環境
python >= 3.7

# 開發工具
pip install black flake8 mypy pytest pytest-cov

# GUI 開發
pip install PyQt5 PyQt5-tools

# 文檔工具
pip install sphinx sphinx-rtd-theme
```

### IDE 設定

#### VS Code 設定 (.vscode/settings.json)

```json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests/"],
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true
    }
}
```

#### PyCharm 設定

1. 設定 Python 解釋器為虛擬環境
2. 啟用 Black 格式化工具
3. 設定 Flake8 為程式碼檢查工具
4. 配置 Pytest 為測試執行器

### 虛擬環境管理

```bash
# 使用 venv
python -m venv venv
source venv/bin/activate  # Linux/macOS
venv\Scripts\activate     # Windows

# 使用 conda
conda create -n exchange-analyzer python=3.9
conda activate exchange-analyzer

# 使用 poetry
poetry install
poetry shell
```

## 📁 程式碼組織

### 目錄結構說明

```
src/exchange_log_analyzer/
├── __init__.py              # 套件初始化，定義公開 API
├── main.py                  # 應用程式入口點
├── core/                    # 核心業務邏輯
│   ├── __init__.py
│   ├── parser.py            # 日誌解析核心邏輯
│   ├── config.py            # 設定管理
│   └── version.py           # 版本資訊
├── gui/                     # 圖形使用者介面
│   ├── __init__.py
│   ├── main_window.py       # 主視窗實作
│   ├── dialogs.py           # 對話框元件
│   └── workers.py           # 背景工作執行緒
└── utils/                   # 工具函式
    ├── __init__.py
    ├── helpers.py           # 輔助函式
    └── constants.py         # 常數定義
```

### 命名規範

#### 檔案命名
- 模組檔案: `snake_case.py`
- 測試檔案: `test_module_name.py`
- 設定檔案: `config.py`, `settings.py`

#### 程式碼命名
```python
# 類別: PascalCase
class LogParser:
    pass

# 函式和變數: snake_case
def parse_log_file():
    file_path = "example.log"

# 常數: UPPER_SNAKE_CASE
MAX_FILE_SIZE = 500 * 1024 * 1024

# 私有成員: 前綴底線
class MyClass:
    def __init__(self):
        self._private_var = None
        self.__very_private = None
```

### 模組設計模式

#### 工廠模式 (Factory Pattern)
```python
class ParserFactory:
    @staticmethod
    def create_parser(parser_type: str) -> LogParser:
        if parser_type == "exchange":
            return ExchangeLogParser()
        elif parser_type == "iis":
            return IISLogParser()
        else:
            raise ValueError(f"Unknown parser type: {parser_type}")
```

#### 觀察者模式 (Observer Pattern)
```python
class ProgressObserver:
    def update(self, progress: int, message: str):
        pass

class LogProcessor:
    def __init__(self):
        self.observers = []
    
    def add_observer(self, observer: ProgressObserver):
        self.observers.append(observer)
    
    def notify_progress(self, progress: int, message: str):
        for observer in self.observers:
            observer.update(progress, message)
```

## 🔄 開發流程

### Git 工作流程

```bash
# 1. 建立功能分支
git checkout -b feature/new-feature

# 2. 進行開發
# ... 編寫程式碼 ...

# 3. 提交變更
git add .
git commit -m "feat: add new feature"

# 4. 推送分支
git push origin feature/new-feature

# 5. 建立 Pull Request
# 在 GitHub 上建立 PR

# 6. 程式碼審查
# 等待審查和合併

# 7. 清理分支
git checkout main
git pull origin main
git branch -d feature/new-feature
```

### 程式碼審查檢查清單

- [ ] 程式碼遵循 PEP 8 風格
- [ ] 所有函式都有適當的文件字串
- [ ] 新功能包含對應的測試
- [ ] 測試覆蓋率維持在 80% 以上
- [ ] 沒有明顯的效能問題
- [ ] 錯誤處理適當
- [ ] 日誌記錄充足
- [ ] 文件已更新

### 發布流程

```bash
# 1. 更新版本號
# 編輯 src/exchange_log_analyzer/__init__.py
# 編輯 src/exchange_log_analyzer/core/version.py
# 編輯 setup.py 和 pyproject.toml

# 2. 更新 CHANGELOG.md
# 記錄新功能、修復和變更

# 3. 建立發布標籤
git tag -a v1.0.10 -m "Release version 1.0.10"
git push origin v1.0.10

# 4. 建立發布套件
python -m build

# 5. 上傳到 PyPI
python -m twine upload dist/*
```

## 🧪 測試策略

### 測試金字塔

```
    /\
   /  \     E2E Tests (少量)
  /____\    
 /      \   Integration Tests (適量)
/__________\ Unit Tests (大量)
```

### 測試類型

#### 單元測試
```python
# tests/unit/test_parser.py
def test_parse_valid_log(sample_log_file):
    parser = LogParser()
    result = parser.parse_file(sample_log_file)
    
    assert not result.empty
    assert 'date-time' in result.columns
```

#### 整合測試
```python
# tests/integration/test_workflow.py
def test_complete_workflow(temp_directory):
    config = Config(log_folder=temp_directory)
    parser = LogParser()
    
    # 測試完整工作流程
    files = config.find_log_files()
    data = parser.parse_file(files[0])
    filtered = parser.filter_data(data, keyword="test")
    
    assert not filtered.empty
```

#### GUI 測試
```python
# tests/gui/test_main_window.py
@pytest.mark.gui
def test_main_window_creation(qt_app):
    window = LogAnalyzer()
    assert window.windowTitle() == "Exchange 追蹤日誌分析工具 v1.0.9"
```

### 測試執行

```bash
# 執行所有測試
pytest

# 執行特定類型的測試
pytest -m unit          # 只執行單元測試
pytest -m integration   # 只執行整合測試
pytest -m "not slow"    # 跳過慢速測試

# 測試覆蓋率
pytest --cov=src/exchange_log_analyzer --cov-report=html

# 平行執行測試
pytest -n auto
```

## ⚡ 效能考量

### 記憶體管理

```python
# 使用生成器處理大型檔案
def read_log_lines(file_path: str):
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            if not line.startswith('#'):
                yield line.strip()

# 分批處理資料
def process_large_dataframe(df: pd.DataFrame, chunk_size: int = 1000):
    for i in range(0, len(df), chunk_size):
        chunk = df.iloc[i:i+chunk_size]
        yield process_chunk(chunk)
```

### 效能監控

```python
import time
import logging
from functools import wraps

def performance_monitor(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        execution_time = end_time - start_time
        logging.info(f"{func.__name__} executed in {execution_time:.2f} seconds")
        
        return result
    return wrapper

@performance_monitor
def parse_large_file(file_path: str):
    # 處理大型檔案
    pass
```

### 快取策略

```python
from functools import lru_cache

class LogParser:
    @lru_cache(maxsize=128)
    def get_file_headers(self, file_path: str) -> list:
        """快取檔案標頭資訊"""
        with open(file_path, 'r') as f:
            for line in f:
                if line.startswith('#Fields:'):
                    return line.replace('#Fields: ', '').strip().split(',')
        return []
```

## 🚀 部署指南

### 建立可執行檔

```bash
# 使用 PyInstaller
pip install pyinstaller
pyinstaller --onefile --windowed main_new.py

# 使用 cx_Freeze
pip install cx_Freeze
python setup_cx_freeze.py build
```

### Docker 部署

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY src/ ./src/
COPY main_new.py .

CMD ["python", "main_new.py"]
```

### 持續整合 (CI/CD)

```yaml
# .github/workflows/ci.yml
name: CI

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.7, 3.8, 3.9, 3.10, 3.11]
    
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v2
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .[dev]
    
    - name: Run tests
      run: |
        pytest --cov=src/exchange_log_analyzer
    
    - name: Upload coverage
      uses: codecov/codecov-action@v1
```

---

## 📞 開發支援

如果您在開發過程中遇到問題：

- 📧 聯絡開發團隊: <EMAIL>
- 📖 查看 [CONTRIBUTING.md](../CONTRIBUTING.md)
- 🐛 回報問題: [GitHub Issues](https://github.com/jackycj0830/ExchangeLogAnalyzer/issues)
- 💬 討論: [GitHub Discussions](https://github.com/jackycj0830/ExchangeLogAnalyzer/discussions)
