#!/usr/bin/env python3
"""
Setup script for Exchange Log Analyzer.

This script provides installation and packaging functionality for the application.
"""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    """Read the README.md file for the long description."""
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return ""

# Read requirements
def read_requirements():
    """Read requirements from requirements.txt."""
    req_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    requirements = []
    if os.path.exists(req_path):
        with open(req_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    # Remove version constraints for basic requirements
                    if '>=' in line:
                        package = line.split('>=')[0]
                    elif '==' in line:
                        package = line.split('==')[0]
                    else:
                        package = line
                    requirements.append(package)
    return requirements

setup(
    name="exchange-log-analyzer",
    version="1.0.9",
    author="Jacky Zou",
    author_email="<EMAIL>",
    description="A tool for analyzing Microsoft Exchange message tracking logs",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/jackycj0830/ExchangeLogAnalyzer",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: System Administrators",
        "Intended Audience :: Information Technology",
        "Topic :: System :: Systems Administration",
        "Topic :: Communications :: Email",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
        "Environment :: X11 Applications :: Qt",
    ],
    python_requires=">=3.7",
    install_requires=[
        "pandas>=1.3.0",
        "PyQt5>=5.15.0",
        "pytz>=2021.1",
    ],
    extras_require={
        "smb": [
            "smbprotocol>=1.8.0",
            "pysmb>=1.2.0",
        ],
        "dev": [
            "pytest>=6.0.0",
            "pytest-qt>=4.0.0",
            "black>=21.0.0",
            "flake8>=3.9.0",
        ],
        "docs": [
            "sphinx>=4.0.0",
            "sphinx-rtd-theme>=0.5.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "exchange-log-analyzer=exchange_log_analyzer.main:main",
        ],
        "gui_scripts": [
            "exchange-log-analyzer-gui=exchange_log_analyzer.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "exchange_log_analyzer": ["*.ico", "*.svg"],
    },
    keywords="exchange, email, log, analysis, tracking, microsoft",
    project_urls={
        "Bug Reports": "https://github.com/jackycj0830/ExchangeLogAnalyzer/issues",
        "Source": "https://github.com/jackycj0830/ExchangeLogAnalyzer",
        "Documentation": "https://github.com/jackycj0830/ExchangeLogAnalyzer/wiki",
    },
)
