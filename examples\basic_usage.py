#!/usr/bin/env python3
"""
Exchange Log Analyzer - 基本使用範例

這個範例展示如何使用 Exchange Log Analyzer 的核心功能來解析和分析日誌檔案。
"""

import os
import sys
from datetime import datetime, timedelta

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from exchange_log_analyzer.core.parser import LogParser
from exchange_log_analyzer.core.config import Config
from exchange_log_analyzer.utils.helpers import format_datetime, format_file_size


def main():
    """主要範例函式"""
    print("Exchange Log Analyzer - 基本使用範例")
    print("=" * 50)
    
    # 1. 初始化組件
    print("\n1. 初始化組件...")
    config = Config()
    parser = LogParser(timezone='Asia/Taipei')  # 使用台北時區
    
    # 2. 設定日誌檔案路徑
    print("\n2. 設定日誌檔案路徑...")
    
    # 您可以修改這個路徑指向實際的 Exchange 日誌檔案
    log_folder = r"C:\Program Files\Microsoft\Exchange Server\V15\TransportRoles\Logs\MessageTracking"
    
    # 如果預設路徑不存在，使用範例檔案
    if not os.path.exists(log_folder):
        print(f"預設路徑不存在: {log_folder}")
        print("請修改 log_folder 變數指向您的 Exchange 日誌檔案路徑")
        
        # 建立範例日誌檔案用於示範
        example_log = create_example_log()
        log_folder = os.path.dirname(example_log)
        print(f"使用範例檔案: {example_log}")
    
    # 3. 尋找日誌檔案
    print(f"\n3. 在 {log_folder} 中尋找日誌檔案...")
    config.log_folder = log_folder
    log_files = config.find_log_files()
    
    if not log_files:
        print("找不到任何 .log 檔案")
        return
    
    print(f"找到 {len(log_files)} 個日誌檔案:")
    for i, file_path in enumerate(log_files[:5], 1):  # 只顯示前5個
        file_size = format_file_size(os.path.getsize(file_path))
        print(f"  {i}. {os.path.basename(file_path)} ({file_size})")
    
    if len(log_files) > 5:
        print(f"  ... 還有 {len(log_files) - 5} 個檔案")
    
    # 4. 解析第一個日誌檔案
    print(f"\n4. 解析日誌檔案: {os.path.basename(log_files[0])}")
    df = parser.parse_file(log_files[0])
    
    if df.empty:
        print("日誌檔案解析失敗或檔案為空")
        return
    
    print(f"成功解析 {len(df)} 筆記錄")
    print(f"欄位: {', '.join(df.columns.tolist())}")
    
    # 5. 顯示基本統計資訊
    print(f"\n5. 基本統計資訊:")
    print(f"  總記錄數: {len(df)}")
    
    if 'event-id' in df.columns:
        event_counts = df['event-id'].value_counts()
        print(f"  事件類型分布:")
        for event, count in event_counts.head().items():
            print(f"    {event}: {count}")
    
    if 'date-time' in df.columns and not df['date-time'].isna().all():
        min_date = df['date-time'].min()
        max_date = df['date-time'].max()
        print(f"  時間範圍: {format_datetime(min_date)} 到 {format_datetime(max_date)}")
    
    # 6. 關鍵字搜尋範例
    print(f"\n6. 關鍵字搜尋範例:")
    search_keywords = ['RECEIVE', 'SEND', 'FAIL']
    
    for keyword in search_keywords:
        filtered_df = parser.filter_data(df, keyword=keyword)
        print(f"  '{keyword}': {len(filtered_df)} 筆記錄")
    
    # 7. 日期範圍篩選範例
    print(f"\n7. 日期範圍篩選範例:")
    
    if 'date-time' in df.columns and not df['date-time'].isna().all():
        # 取得最近一小時的資料
        latest_time = df['date-time'].max()
        one_hour_ago = latest_time - timedelta(hours=1)
        
        recent_df = parser.filter_data(
            df, 
            start_date=one_hour_ago.strftime('%Y-%m-%d %H:%M:%S'),
            end_date=latest_time.strftime('%Y-%m-%d %H:%M:%S')
        )
        print(f"  最近一小時: {len(recent_df)} 筆記錄")
    else:
        print("  無法進行日期篩選（缺少有效的日期時間資料）")
    
    # 8. 組合篩選範例
    print(f"\n8. 組合篩選範例:")
    
    if 'date-time' in df.columns and not df['date-time'].isna().all():
        latest_time = df['date-time'].max()
        one_hour_ago = latest_time - timedelta(hours=1)
        
        combined_df = parser.filter_data(
            df,
            keyword='RECEIVE',
            start_date=one_hour_ago.strftime('%Y-%m-%d %H:%M:%S'),
            end_date=latest_time.strftime('%Y-%m-%d %H:%M:%S')
        )
        print(f"  最近一小時的 RECEIVE 事件: {len(combined_df)} 筆記錄")
    
    # 9. 顯示範例資料
    print(f"\n9. 範例資料 (前3筆):")
    if not df.empty:
        # 選擇要顯示的欄位
        display_columns = []
        for col in ['date-time', 'event-id', 'sender-address', 'recipient-address', 'message-subject']:
            if col in df.columns:
                display_columns.append(col)
        
        if display_columns:
            sample_df = df[display_columns].head(3)
            for i, (_, row) in enumerate(sample_df.iterrows(), 1):
                print(f"\n  記錄 {i}:")
                for col in display_columns:
                    value = row[col]
                    if pd.isna(value):
                        value = "N/A"
                    elif col == 'date-time' and hasattr(value, 'strftime'):
                        value = format_datetime(value)
                    print(f"    {col}: {value}")
    
    print(f"\n範例執行完成！")
    print(f"您可以修改這個範例來適應您的特定需求。")


def create_example_log():
    """建立範例日誌檔案用於示範"""
    import tempfile
    
    example_content = """#Software: Microsoft Exchange Server
#Version: 15.01.2507.012
#Log-type: Message Tracking Log
#Date: 2025-01-20T00:00:00.000Z
#Fields: date-time,client-ip,client-hostname,server-ip,server-hostname,source-context,connector-id,source,event-id,internal-message-id,message-id,network-message-id,recipient-address,recipient-status,total-bytes,recipient-count,related-recipient-address,reference,message-subject,sender-address,return-path,message-info,directionality,tenant-id,original-client-ip,original-server-ip,custom-data
2025-01-20T08:30:15.123Z,************0,client01.domain.com,************,exchange01.domain.com,,Default Frontend EXCHANGE01,SMTP,RECEIVE,08DC5F7F-1234-5678-9ABC-DEF012345678,<<EMAIL>>,,<EMAIL>,250 2.6.0 <<EMAIL>> [InternalId=12345] Queued mail for delivery,1024,1,,,"測試郵件 1",<EMAIL>,<EMAIL>,,1,,************0,************,
2025-01-20T08:30:16.456Z,************0,client01.domain.com,************,exchange01.domain.com,,Default Frontend EXCHANGE01,SMTP,SEND,08DC5F7F-1234-5678-9ABC-DEF012345678,<<EMAIL>>,,<EMAIL>,250 2.6.0 <<EMAIL>> [InternalId=12345] Queued mail for delivery,1024,1,,,"測試郵件 1",<EMAIL>,<EMAIL>,,1,,************0,************,
2025-01-20T08:31:20.789Z,************1,client02.domain.com,************,exchange01.domain.com,,Default Frontend EXCHANGE01,SMTP,RECEIVE,09EC6F8F-2345-6789-ABCD-EF0123456789,<<EMAIL>>,,<EMAIL>,250 2.6.0 <<EMAIL>> [InternalId=12346] Queued mail for delivery,2048,1,,,"重要通知",<EMAIL>,<EMAIL>,,1,,************1,************,
2025-01-20T08:31:21.012Z,************1,client02.domain.com,************,exchange01.domain.com,,Default Frontend EXCHANGE01,SMTP,SEND,09EC6F8F-2345-6789-ABCD-EF0123456789,<<EMAIL>>,,<EMAIL>,250 2.6.0 <<EMAIL>> [InternalId=12346] Queued mail for delivery,2048,1,,,"重要通知",<EMAIL>,<EMAIL>,,1,,************1,************,
2025-01-20T08:32:30.345Z,************2,client03.domain.com,************,exchange01.domain.com,,Default Frontend EXCHANGE01,SMTP,FAIL,0AED7F9F-3456-789A-BCDE-F01234567890,<<EMAIL>>,,<EMAIL>,550 5.1.1 User unknown,512,1,,,"失敗測試",<EMAIL>,<EMAIL>,,1,,************2,************,
"""
    
    # 建立臨時檔案
    temp_dir = tempfile.gettempdir()
    example_file = os.path.join(temp_dir, "example_exchange.log")
    
    with open(example_file, 'w', encoding='utf-8') as f:
        f.write(example_content)
    
    return example_file


if __name__ == '__main__':
    # 確保必要的模組可以匯入
    try:
        import pandas as pd
        main()
    except ImportError as e:
        print(f"缺少必要的套件: {e}")
        print("請執行: pip install -r requirements.txt")
    except Exception as e:
        print(f"執行錯誤: {e}")
        import traceback
        traceback.print_exc()
