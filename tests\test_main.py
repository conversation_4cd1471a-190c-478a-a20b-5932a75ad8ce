"""
Tests for the main application entry point.

This module contains tests for the main application startup and initialization.
"""

import pytest
import sys
import os
from unittest.mock import patch, MagicMock

# Add src to path for testing
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


class TestMainModule:
    """Test cases for the main module."""
    
    @patch('exchange_log_analyzer.main.QApplication')
    @patch('exchange_log_analyzer.main.LogAnalyzer')
    @patch('exchange_log_analyzer.main.setup_logging')
    def test_main_function_success(self, mock_setup_logging, mock_log_analyzer, mock_qapp):
        """Test successful main function execution."""
        # Setup mocks
        mock_app_instance = MagicMock()
        mock_qapp.return_value = mock_app_instance
        mock_app_instance.exec_.return_value = 0
        
        mock_window = MagicMock()
        mock_log_analyzer.return_value = mock_window
        
        # Import and run main
        from exchange_log_analyzer.main import main
        
        with patch('sys.exit') as mock_exit:
            main()
            
            # Verify calls
            mock_setup_logging.assert_called_once()
            mock_qapp.assert_called_once()
            mock_log_analyzer.assert_called_once()
            mock_window.show.assert_called_once()
            mock_app_instance.exec_.assert_called_once()
            mock_exit.assert_called_once_with(0)
    
    @patch('exchange_log_analyzer.main.QApplication')
    @patch('exchange_log_analyzer.main.LogAnalyzer')
    @patch('exchange_log_analyzer.main.setup_logging')
    def test_main_function_exception(self, mock_setup_logging, mock_log_analyzer, mock_qapp):
        """Test main function with exception."""
        # Setup mocks to raise exception
        mock_log_analyzer.side_effect = Exception("Test exception")
        
        from exchange_log_analyzer.main import main
        
        with patch('sys.exit') as mock_exit:
            with patch('logging.critical') as mock_critical:
                main()
                
                # Verify error handling
                mock_critical.assert_called_once()
                mock_exit.assert_called_once_with(1)
    
    def test_main_module_imports(self):
        """Test that main module imports work correctly."""
        try:
            from exchange_log_analyzer.main import main
            assert callable(main)
        except ImportError as e:
            pytest.fail(f"Failed to import main module: {e}")
    
    @patch('exchange_log_analyzer.main.QApplication')
    def test_application_properties_set(self, mock_qapp):
        """Test that application properties are set correctly."""
        mock_app_instance = MagicMock()
        mock_qapp.return_value = mock_app_instance
        
        with patch('exchange_log_analyzer.main.LogAnalyzer'):
            with patch('sys.exit'):
                from exchange_log_analyzer.main import main
                main()
                
                # Verify application properties are set
                mock_app_instance.setApplicationName.assert_called_with("Exchange Log Analyzer")
                mock_app_instance.setApplicationVersion.assert_called_with("1.0.9")
                mock_app_instance.setOrganizationName.assert_called_with("TPV IT Global Infrastructure Team")


class TestMainNewModule:
    """Test cases for the main_new.py entry point."""
    
    def test_main_new_imports(self):
        """Test that main_new.py can be imported and executed."""
        # Test that the file exists and can be imported
        main_new_path = os.path.join(os.path.dirname(__file__), '..', 'main_new.py')
        assert os.path.exists(main_new_path)
        
        # Test that it contains the expected content
        with open(main_new_path, 'r', encoding='utf-8') as f:
            content = f.read()
            assert 'from exchange_log_analyzer.main import main' in content
            assert 'if __name__ == \'__main__\':' in content
    
    @patch('sys.path')
    def test_main_new_path_setup(self, mock_path):
        """Test that main_new.py sets up the Python path correctly."""
        # This test verifies the path manipulation logic
        mock_path.insert = MagicMock()
        
        # Import the module (this will execute the path setup)
        import importlib.util
        main_new_path = os.path.join(os.path.dirname(__file__), '..', 'main_new.py')
        
        if os.path.exists(main_new_path):
            spec = importlib.util.spec_from_file_location("main_new", main_new_path)
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                
                # Execute the module to test path setup
                try:
                    spec.loader.exec_module(module)
                    # If we get here, the path setup worked
                    assert True
                except Exception:
                    # Path setup or import failed
                    pytest.skip("Could not test main_new path setup")


@pytest.mark.gui
class TestGUIInitialization:
    """Test cases for GUI initialization."""
    
    def test_qt_application_creation(self, qt_app):
        """Test that QApplication can be created."""
        assert qt_app is not None
        assert hasattr(qt_app, 'exec_')
    
    @patch('exchange_log_analyzer.gui.main_window.LogAnalyzer')
    def test_log_analyzer_creation(self, mock_log_analyzer, qt_app):
        """Test that LogAnalyzer window can be created."""
        mock_window = MagicMock()
        mock_log_analyzer.return_value = mock_window
        
        # Try to create the window
        from exchange_log_analyzer.gui.main_window import LogAnalyzer
        window = LogAnalyzer()
        
        assert window is not None


class TestModuleStructure:
    """Test cases for module structure and imports."""
    
    def test_package_structure(self):
        """Test that the package structure is correct."""
        # Test main package
        try:
            import exchange_log_analyzer
            assert hasattr(exchange_log_analyzer, '__version__')
        except ImportError:
            pytest.fail("Could not import main package")
    
    def test_core_modules_import(self):
        """Test that core modules can be imported."""
        modules_to_test = [
            'exchange_log_analyzer.core.parser',
            'exchange_log_analyzer.core.config',
            'exchange_log_analyzer.core.version',
        ]
        
        for module_name in modules_to_test:
            try:
                __import__(module_name)
            except ImportError as e:
                pytest.fail(f"Could not import {module_name}: {e}")
    
    def test_gui_modules_import(self):
        """Test that GUI modules can be imported."""
        gui_modules = [
            'exchange_log_analyzer.gui.main_window',
            'exchange_log_analyzer.gui.dialogs',
            'exchange_log_analyzer.gui.workers',
        ]
        
        for module_name in gui_modules:
            try:
                __import__(module_name)
            except ImportError as e:
                # GUI modules might fail if PyQt5 is not available
                if 'PyQt5' in str(e):
                    pytest.skip(f"PyQt5 not available for {module_name}")
                else:
                    pytest.fail(f"Could not import {module_name}: {e}")
    
    def test_utils_modules_import(self):
        """Test that utility modules can be imported."""
        utils_modules = [
            'exchange_log_analyzer.utils.helpers',
            'exchange_log_analyzer.utils.constants',
        ]
        
        for module_name in utils_modules:
            try:
                __import__(module_name)
            except ImportError as e:
                pytest.fail(f"Could not import {module_name}: {e}")
    
    def test_version_consistency(self):
        """Test that version information is consistent across modules."""
        try:
            from exchange_log_analyzer import __version__ as main_version
            from exchange_log_analyzer.core.version import __version__ as core_version
            
            assert main_version == core_version
        except ImportError:
            pytest.skip("Could not import version information")


if __name__ == '__main__':
    pytest.main([__file__])
