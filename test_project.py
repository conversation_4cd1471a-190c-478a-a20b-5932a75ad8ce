#!/usr/bin/env python3
"""
簡單的專案測試腳本

這個腳本用來驗證 Exchange Log Analyzer 專案是否正確設置。
"""

import sys
import os

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """測試模組匯入"""
    print("測試模組匯入...")
    
    try:
        import exchange_log_analyzer
        print("✓ 主套件匯入成功")
        print(f"  版本: {exchange_log_analyzer.__version__}")
        print(f"  作者: {exchange_log_analyzer.__author__}")
    except ImportError as e:
        print(f"✗ 主套件匯入失敗: {e}")
        return False
    
    try:
        from exchange_log_analyzer.core.parser import LogParser
        print("✓ LogParser 匯入成功")
    except ImportError as e:
        print(f"✗ LogParser 匯入失敗: {e}")
        return False
    
    try:
        from exchange_log_analyzer.core.config import Config
        print("✓ Config 匯入成功")
    except ImportError as e:
        print(f"✗ Config 匯入失敗: {e}")
        return False
    
    try:
        from exchange_log_analyzer.utils.helpers import format_datetime
        print("✓ 工具函式匯入成功")
    except ImportError as e:
        print(f"✗ 工具函式匯入失敗: {e}")
        return False
    
    return True

def test_basic_functionality():
    """測試基本功能"""
    print("\n測試基本功能...")
    
    try:
        from exchange_log_analyzer.core.parser import LogParser
        from exchange_log_analyzer.core.config import Config
        
        # 測試 Config
        config = Config()
        print("✓ Config 初始化成功")
        
        # 測試 LogParser
        parser = LogParser()
        print("✓ LogParser 初始化成功")
        
        # 測試基本方法
        log_files = config.find_log_files(".")
        print(f"✓ 檔案搜尋功能正常 (找到 {len(log_files)} 個 .log 檔案)")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能測試失敗: {e}")
        return False

def test_project_structure():
    """測試專案結構"""
    print("\n測試專案結構...")
    
    required_files = [
        'src/exchange_log_analyzer/__init__.py',
        'src/exchange_log_analyzer/main.py',
        'src/exchange_log_analyzer/core/__init__.py',
        'src/exchange_log_analyzer/core/parser.py',
        'src/exchange_log_analyzer/core/config.py',
        'src/exchange_log_analyzer/gui/__init__.py',
        'src/exchange_log_analyzer/utils/__init__.py',
        'tests/__init__.py',
        'tests/conftest.py',
        'requirements.txt',
        'setup.py',
        'README.md'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} (缺少)")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n缺少 {len(missing_files)} 個必要檔案")
        return False
    else:
        print("\n✓ 所有必要檔案都存在")
        return True

def main():
    """主測試函式"""
    print("Exchange Log Analyzer 專案測試")
    print("=" * 50)
    
    tests = [
        ("專案結構", test_project_structure),
        ("模組匯入", test_imports),
        ("基本功能", test_basic_functionality)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}測試:")
        print("-" * 30)
        success = test_func()
        results.append((test_name, success))
    
    print("\n" + "=" * 50)
    print("測試結果摘要:")
    print("=" * 50)
    
    all_passed = True
    for test_name, success in results:
        status = "✓ 通過" if success else "✗ 失敗"
        print(f"{test_name}: {status}")
        if not success:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有測試都通過了！專案設置正確。")
        print("\n下一步:")
        print("1. 執行 'python main_new.py' 啟動應用程式")
        print("2. 執行 'python scripts/run_tests.py' 進行完整測試")
        print("3. 查看 README.md 了解更多使用方式")
    else:
        print("\n❌ 有些測試失敗了。請檢查專案設置。")
    
    return all_passed

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"測試執行錯誤: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
