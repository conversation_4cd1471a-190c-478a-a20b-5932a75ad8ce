#!/usr/bin/env python3
"""
Exchange Log Analyzer - Main Entry Point

A tool for analyzing Microsoft Exchange message tracking logs with a GUI interface.

Usage:
    python main_new.py

Author: <PERSON><PERSON>ail: <EMAIL>
Version: 1.0.9
"""

import sys
import os

# Add src directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

# Import and run the main application
from exchange_log_analyzer.main import main

if __name__ == '__main__':
    main()
