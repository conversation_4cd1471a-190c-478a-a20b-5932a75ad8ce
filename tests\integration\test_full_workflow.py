"""
Integration tests for Exchange Log Analyzer.

This module contains integration tests that test the complete workflow
of the application.
"""

import pytest
import os
import tempfile
import pandas as pd
from datetime import datetime

from exchange_log_analyzer.core.parser import LogParser
from exchange_log_analyzer.core.config import Config


@pytest.mark.integration
class TestFullWorkflow:
    """Integration tests for complete application workflow."""
    
    def test_complete_log_processing_workflow(self, multiple_log_files):
        """Test complete workflow from file loading to filtering."""
        # Initialize components
        config = Config()
        parser = LogParser()
        
        # Step 1: Load and parse multiple log files
        all_data = []
        for log_file in multiple_log_files:
            df = parser.parse_file(log_file)
            assert not df.empty
            all_data.append(df)
        
        # Step 2: Combine all data
        combined_data = pd.concat(all_data, ignore_index=True)
        assert len(combined_data) == len(multiple_log_files) * 2  # 2 entries per file
        
        # Step 3: Filter by keyword
        filtered_data = parser.filter_data(combined_data, keyword="RECEIVE")
        assert len(filtered_data) == len(multiple_log_files)  # 1 RECEIVE per file
        
        # Step 4: Filter by date range
        start_date = "2025-01-20 08:30:00"
        end_date = "2025-01-20 08:32:00"
        date_filtered = parser.filter_data(
            combined_data, 
            start_date=start_date, 
            end_date=end_date
        )
        assert not date_filtered.empty
        
        # Step 5: Combined filtering
        combined_filtered = parser.filter_data(
            combined_data,
            keyword="user",
            start_date=start_date,
            end_date=end_date
        )
        assert not combined_filtered.empty
    
    def test_config_and_parser_integration(self, temp_directory):
        """Test integration between Config and LogParser."""
        # Create test log files
        log_files = []
        for i in range(2):
            log_content = f"""#Software: Microsoft Exchange Server
#Version: 15.01.2507.012
#Log-type: Message Tracking Log
#Date: 2025-01-20T00:00:00.000Z
#Fields: date-time,event-id,recipient-address,sender-address,message-subject
2025-01-20T08:3{i}:00.000Z,RECEIVE,user{i}@domain.com,<EMAIL>,Test Subject {i}
2025-01-20T08:3{i}:30.000Z,SEND,user{i}@domain.com,<EMAIL>,Test Subject {i}
"""
            log_file = os.path.join(temp_directory, f"exchange_{i}.log")
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(log_content)
            log_files.append(log_file)
        
        # Use Config to find files
        config = Config(log_folder=temp_directory)
        found_files = config.find_log_files()
        
        assert len(found_files) == 2
        assert all(f in found_files for f in log_files)
        
        # Use LogParser to process found files
        parser = LogParser()
        all_data = []
        
        for file_path in found_files:
            df = parser.parse_file(file_path)
            assert not df.empty
            all_data.append(df)
        
        # Combine and verify
        combined_data = pd.concat(all_data, ignore_index=True)
        assert len(combined_data) == 4  # 2 entries per file, 2 files
        assert 'date-time' in combined_data.columns
        assert 'event-id' in combined_data.columns
    
    def test_error_handling_workflow(self, temp_directory):
        """Test workflow with various error conditions."""
        config = Config(log_folder=temp_directory)
        parser = LogParser()
        
        # Create mixed files (valid, invalid, empty)
        files_data = [
            ("valid.log", """#Software: Microsoft Exchange Server
#Fields: date-time,event-id,recipient-address
2025-01-20T08:30:00.000Z,RECEIVE,<EMAIL>
"""),
            ("invalid.log", "This is not a valid log file"),
            ("empty.log", ""),
            ("not_a_log.txt", "Text file content")
        ]
        
        for filename, content in files_data:
            file_path = os.path.join(temp_directory, filename)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
        
        # Find log files (should only find .log files)
        found_files = config.find_log_files()
        assert len(found_files) == 3  # Only .log files
        
        # Process files (some will fail gracefully)
        successful_data = []
        for file_path in found_files:
            df = parser.parse_file(file_path)
            if not df.empty:
                successful_data.append(df)
        
        # Should have at least one successful parse
        assert len(successful_data) >= 1
        
        # Combine successful data
        if successful_data:
            combined_data = pd.concat(successful_data, ignore_index=True)
            assert not combined_data.empty
    
    def test_large_dataset_workflow(self, temp_directory):
        """Test workflow with larger dataset."""
        # Create a larger log file
        large_log_content = """#Software: Microsoft Exchange Server
#Version: 15.01.2507.012
#Log-type: Message Tracking Log
#Date: 2025-01-20T00:00:00.000Z
#Fields: date-time,event-id,recipient-address,sender-address,message-subject,total-bytes
"""
        
        # Add many entries
        for i in range(100):
            hour = 8 + (i // 60)
            minute = i % 60
            large_log_content += f"2025-01-20T{hour:02d}:{minute:02d}:00.000Z,RECEIVE,user{i}@domain.com,<EMAIL>,Test Subject {i},1024\n"
            large_log_content += f"2025-01-20T{hour:02d}:{minute:02d}:30.000Z,SEND,user{i}@domain.com,<EMAIL>,Test Subject {i},1024\n"
        
        large_log_file = os.path.join(temp_directory, "large.log")
        with open(large_log_file, 'w', encoding='utf-8') as f:
            f.write(large_log_content)
        
        # Process large file
        config = Config(log_folder=temp_directory)
        parser = LogParser()
        
        found_files = config.find_log_files()
        assert len(found_files) == 1
        
        df = parser.parse_file(found_files[0])
        assert len(df) == 200  # 100 RECEIVE + 100 SEND
        
        # Test filtering on large dataset
        receive_only = parser.filter_data(df, keyword="RECEIVE")
        assert len(receive_only) == 100
        
        # Test date range filtering
        start_date = "2025-01-20 08:30:00"
        end_date = "2025-01-20 09:00:00"
        date_filtered = parser.filter_data(df, start_date=start_date, end_date=end_date)
        assert not date_filtered.empty
        assert len(date_filtered) < len(df)
    
    def test_timezone_handling_workflow(self, sample_log_file):
        """Test workflow with different timezone settings."""
        # Test with different timezones
        timezones = ['UTC', 'Asia/Taipei', 'US/Eastern']
        
        results = {}
        for tz in timezones:
            parser = LogParser(timezone=tz)
            df = parser.parse_file(sample_log_file)
            
            assert not df.empty
            assert 'date-time' in df.columns
            
            # Store first datetime for comparison
            if not df['date-time'].isna().all():
                first_dt = df['date-time'].iloc[0]
                results[tz] = first_dt
        
        # Verify that different timezones produce different results
        if len(results) > 1:
            timezone_values = list(results.values())
            # At least some should be different (unless all are UTC)
            assert len(set(str(dt) for dt in timezone_values)) >= 1
    
    @pytest.mark.slow
    def test_performance_workflow(self, temp_directory):
        """Test workflow performance with multiple files."""
        import time
        
        # Create multiple log files
        num_files = 5
        entries_per_file = 200
        
        for file_idx in range(num_files):
            log_content = """#Software: Microsoft Exchange Server
#Fields: date-time,event-id,recipient-address,sender-address
"""
            for entry_idx in range(entries_per_file):
                minute = entry_idx % 60
                hour = 8 + (entry_idx // 60)
                log_content += f"2025-01-20T{hour:02d}:{minute:02d}:00.000Z,RECEIVE,user{entry_idx}@domain.com,<EMAIL>\n"
            
            log_file = os.path.join(temp_directory, f"perf_test_{file_idx}.log")
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(log_content)
        
        # Measure processing time
        config = Config(log_folder=temp_directory)
        parser = LogParser()
        
        start_time = time.time()
        
        found_files = config.find_log_files()
        assert len(found_files) == num_files
        
        all_data = []
        for file_path in found_files:
            df = parser.parse_file(file_path)
            all_data.append(df)
        
        combined_data = pd.concat(all_data, ignore_index=True)
        
        # Test filtering performance
        filtered_data = parser.filter_data(combined_data, keyword="user")
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Verify results
        assert len(combined_data) == num_files * entries_per_file
        assert len(filtered_data) == num_files * entries_per_file  # All entries contain "user"
        
        # Performance assertion (should complete within reasonable time)
        assert processing_time < 30  # Should complete within 30 seconds
