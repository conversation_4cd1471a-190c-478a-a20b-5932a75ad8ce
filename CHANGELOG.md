# 更新日誌 (Changelog)

本檔案記錄了 Exchange Log Analyzer 專案的所有重要變更。

格式基於 [Keep a Changelog](https://keepachangelog.com/zh-TW/1.0.0/)，
並且本專案遵循 [語意化版本](https://semver.org/lang/zh-TW/)。

## [未發布]

### 新增
- 完整的專案重構，採用模組化架構
- 完整的測試框架（單元測試和整合測試）
- 詳細的開發文檔和貢獻指南
- 標準化的 Python 套件結構
- 支援 pip 安裝和 PyPI 發布
- 完整的 README.md 文檔

### 變更
- 重新組織程式碼結構到 src/ 目錄
- 改進錯誤處理和日誌記錄
- 優化記憶體使用和效能
- 更新依賴套件版本

### 修復
- 修復時區轉換問題
- 改進檔案編碼處理
- 修復大型檔案處理的記憶體問題

## [1.0.9] - 2025-07-19

### 新增
- 新增「連線至 Exchange 下載 Log」功能
- 支援 SMB 協定連線（使用 pysmb）
- 遠端檔案下載功能

### 改進
- 優化使用者介面
- 改進錯誤處理

## [1.0.8] - 2025-07-17

### 新增
- 增加多重進階查詢關鍵字的模糊查詢功能
- 改進搜尋演算法

### 改進
- 提升搜尋效能
- 優化查詢結果顯示

## [1.0.7] - 2025-05-26

### 新增
- 增加查詢進度條功能
- 背景處理支援

### 改進
- 改善大型檔案處理體驗
- 優化使用者回饋機制

## [1.0.6] - 2025-05-19

### 發布
- 正式發行版 (By Jacky Zou)
- 穩定版本發布

### 改進
- 程式碼穩定性提升
- 效能優化

## [1.0.1] - 2025-05-15

### 改進
- 改進讀取效能
- 增加分頁功能

### 修復
- 修復記憶體洩漏問題
- 改善大型檔案處理

## [1.0.0] - 2025-05-14

### 新增
- 初階測試版本
- 查看所有 Exchange 日誌 Log 文件功能
- 顯示版本信息功能
- 基本的 GUI 介面

### 功能
- 📁 LOG 檔案載入
- 🔍 關鍵字搜尋
- 📅 日期範圍篩選
- 📊 表格顯示
- 💾 CSV 匯出

---

## 版本說明

### [未發布] - 即將發布的變更
包含尚未發布但已完成的功能和修復。

### [1.0.9] - 最新穩定版
- 主要新增 SMB 連線功能
- 支援遠端 Exchange 伺服器連線

### [1.0.8] - 搜尋功能增強版
- 改進搜尋功能
- 支援多關鍵字查詢

### [1.0.7] - 使用者體驗改進版
- 新增進度條
- 改善大型檔案處理

### [1.0.6] - 正式發行版
- 第一個正式穩定版本
- 適合生產環境使用

### [1.0.1] - 效能改進版
- 主要改進效能和記憶體使用
- 新增分頁功能

### [1.0.0] - 初始版本
- 第一個可用版本
- 基本功能實作

---

## 變更類型說明

- **新增**: 新功能
- **變更**: 現有功能的變更
- **棄用**: 即將移除的功能
- **移除**: 已移除的功能
- **修復**: 錯誤修復
- **安全性**: 安全性相關的修復

---

## 貢獻者

感謝所有為此專案做出貢獻的開發者：

- **Jacky Zou** (鄒嘉駿) - 主要開發者
- **TPV IT Global Infrastructure Team** - 支援團隊

---

## 支援

如果您在使用過程中遇到問題，請：

1. 查看 [README.md](README.md) 中的常見問題
2. 搜尋現有的 [Issues](https://github.com/jackycj0830/ExchangeLogAnalyzer/issues)
3. 建立新的 Issue 回報問題
4. 聯絡開發團隊：<EMAIL>
