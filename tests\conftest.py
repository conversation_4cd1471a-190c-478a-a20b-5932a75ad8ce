"""
Pytest configuration and shared fixtures for Exchange Log Analyzer tests.

This module contains pytest configuration and fixtures that are shared
across multiple test modules.
"""

import pytest
import tempfile
import os
import pandas as pd
from datetime import datetime
import sys
from pathlib import Path

# Add src directory to Python path for testing
src_dir = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_dir))

from exchange_log_analyzer.core.parser import LogParser
from exchange_log_analyzer.core.config import Config


@pytest.fixture
def sample_log_content():
    """Sample Exchange log content for testing."""
    return """#Software: Microsoft Exchange Server
#Version: 15.01.2507.012
#Log-type: Message Tracking Log
#Date: 2025-01-20T00:00:00.000Z
#Fields: date-time,client-ip,client-hostname,server-ip,server-hostname,source-context,connector-id,source,event-id,internal-message-id,message-id,network-message-id,recipient-address,recipient-status,total-bytes,recipient-count,related-recipient-address,reference,message-subject,sender-address,return-path,message-info,directionality,tenant-id,original-client-ip,original-server-ip,custom-data
2025-01-20T08:30:15.123Z,*************,client01.domain.com,************,exchange01.domain.com,,Default Frontend EXCHANGE01,SMTP,RECEIVE,08DC5F7F-1234-5678-9ABC-DEF012345678,<<EMAIL>>,,<EMAIL>,250 2.6.0 <<EMAIL>> [InternalId=12345] Queued mail for delivery,1024,1,,,"Test Subject",<EMAIL>,<EMAIL>,,1,,*************,************,
2025-01-20T08:30:16.456Z,*************,client01.domain.com,************,exchange01.domain.com,,Default Frontend EXCHANGE01,SMTP,SEND,08DC5F7F-1234-5678-9ABC-DEF012345678,<<EMAIL>>,,<EMAIL>,250 2.6.0 <<EMAIL>> [InternalId=12345] Queued mail for delivery,1024,1,,,"Test Subject",<EMAIL>,<EMAIL>,,1,,*************,************,
"""


@pytest.fixture
def sample_log_file(tmp_path, sample_log_content):
    """Create a temporary log file with sample content."""
    log_file = tmp_path / "test.log"
    log_file.write_text(sample_log_content, encoding='utf-8')
    return str(log_file)


@pytest.fixture
def sample_dataframe():
    """Sample DataFrame for testing."""
    data = {
        'date-time': [
            pd.Timestamp('2025-01-20 08:30:15.123', tz='UTC'),
            pd.Timestamp('2025-01-20 08:30:16.456', tz='UTC')
        ],
        'client-ip': ['*************', '*************'],
        'server-hostname': ['exchange01.domain.com', 'exchange01.domain.com'],
        'event-id': ['RECEIVE', 'SEND'],
        'recipient-address': ['<EMAIL>', '<EMAIL>'],
        'sender-address': ['<EMAIL>', '<EMAIL>'],
        'message-subject': ['Test Subject', 'Test Subject'],
        'total-bytes': ['1024', '1024']
    }
    return pd.DataFrame(data)


@pytest.fixture
def log_parser():
    """LogParser instance for testing."""
    return LogParser()


@pytest.fixture
def config():
    """Config instance for testing."""
    return Config()


@pytest.fixture
def temp_directory(tmp_path):
    """Temporary directory for testing."""
    return str(tmp_path)


@pytest.fixture
def multiple_log_files(tmp_path, sample_log_content):
    """Create multiple log files for testing."""
    files = []
    for i in range(3):
        log_file = tmp_path / f"test_{i}.log"
        # Modify content slightly for each file
        modified_content = sample_log_content.replace(
            "08:30:15.123Z", f"08:3{i}:15.123Z"
        ).replace(
            "08:30:16.456Z", f"08:3{i}:16.456Z"
        )
        log_file.write_text(modified_content, encoding='utf-8')
        files.append(str(log_file))
    return files


@pytest.fixture
def invalid_log_file(tmp_path):
    """Create an invalid log file for testing error handling."""
    log_file = tmp_path / "invalid.log"
    log_file.write_text("This is not a valid Exchange log file", encoding='utf-8')
    return str(log_file)


@pytest.fixture
def empty_log_file(tmp_path):
    """Create an empty log file for testing."""
    log_file = tmp_path / "empty.log"
    log_file.write_text("", encoding='utf-8')
    return str(log_file)


@pytest.fixture(scope="session")
def qt_app():
    """QApplication instance for GUI testing."""
    try:
        from PyQt5.QtWidgets import QApplication
        import sys
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        yield app
    except ImportError:
        pytest.skip("PyQt5 not available for GUI testing")


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )
    config.addinivalue_line(
        "markers", "gui: marks tests that require GUI components"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically."""
    for item in items:
        # Add unit marker to tests in unit directory
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        
        # Add integration marker to tests in integration directory
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        
        # Add gui marker to tests that use qt_app fixture
        if "qt_app" in item.fixturenames:
            item.add_marker(pytest.mark.gui)
