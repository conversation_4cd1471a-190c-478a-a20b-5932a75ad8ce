# 貢獻指南 (Contributing Guide)

感謝您對 Exchange Log Analyzer 專案的興趣！我們歡迎各種形式的貢獻，包括錯誤回報、功能建議、程式碼改進和文件更新。

## 📋 目錄

- [如何貢獻](#如何貢獻)
- [開發環境設置](#開發環境設置)
- [程式碼規範](#程式碼規範)
- [提交指南](#提交指南)
- [測試指南](#測試指南)
- [文件撰寫](#文件撰寫)
- [問題回報](#問題回報)
- [功能建議](#功能建議)

## 🤝 如何貢獻

### 貢獻類型

我們歡迎以下類型的貢獻：

- 🐛 **錯誤修復**: 修復已知的程式錯誤
- ✨ **新功能**: 添加新的功能特性
- 📚 **文件改進**: 改善文件、註解和範例
- 🎨 **介面優化**: 改善使用者體驗和介面設計
- ⚡ **效能優化**: 提升程式執行效能
- 🧪 **測試增強**: 添加或改進測試覆蓋率
- 🔧 **工具改進**: 改善開發工具和流程

### 貢獻流程

1. **Fork 專案**到您的 GitHub 帳號
2. **建立功能分支**從 `main` 分支
3. **進行開發**並遵循程式碼規範
4. **撰寫測試**確保功能正常運作
5. **更新文件**如有必要
6. **提交變更**並推送到您的分支
7. **建立 Pull Request**到主專案

## 🛠 開發環境設置

### 系統需求

- Python 3.7 或更高版本
- Git
- 建議使用虛擬環境

### 設置步驟

```bash
# 1. Fork 並複製專案
git clone https://github.com/YOUR_USERNAME/ExchangeLogAnalyzer.git
cd ExchangeLogAnalyzer

# 2. 建立虛擬環境
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate

# 3. 安裝開發依賴
pip install -e .[dev,smb]

# 4. 安裝 pre-commit hooks
pip install pre-commit
pre-commit install

# 5. 驗證安裝
python -m pytest tests/ -v
```

### 開發工具

推薦使用以下開發工具：

- **IDE**: VS Code, PyCharm, 或其他支援 Python 的 IDE
- **程式碼格式化**: Black
- **程式碼檢查**: Flake8
- **型別檢查**: MyPy
- **測試**: Pytest

## 📝 程式碼規範

### Python 程式碼風格

我們遵循 [PEP 8](https://www.python.org/dev/peps/pep-0008/) 程式碼風格指南：

```python
# 好的範例
def parse_log_file(file_path: str, timezone: str = 'UTC') -> pd.DataFrame:
    """
    Parse an Exchange log file.
    
    Args:
        file_path (str): Path to the log file.
        timezone (str): Timezone for datetime conversion.
        
    Returns:
        pd.DataFrame: Parsed log data.
    """
    try:
        # 實作邏輯
        return result
    except Exception as e:
        logger.error(f"Failed to parse {file_path}: {e}")
        return pd.DataFrame()
```

### 命名規範

- **變數和函式**: 使用 `snake_case`
- **類別**: 使用 `PascalCase`
- **常數**: 使用 `UPPER_SNAKE_CASE`
- **私有成員**: 以底線 `_` 開頭

### 文件字串

所有公開的函式和類別都必須包含文件字串：

```python
def filter_logs(df: pd.DataFrame, keyword: str = None) -> pd.DataFrame:
    """
    Filter log data based on keyword.
    
    Args:
        df (pd.DataFrame): The log data to filter.
        keyword (str, optional): Keyword to search for.
        
    Returns:
        pd.DataFrame: Filtered log data.
        
    Raises:
        ValueError: If df is not a valid DataFrame.
    """
```

### 程式碼檢查

在提交前請執行以下檢查：

```bash
# 程式碼格式化
black src/ tests/

# 程式碼檢查
flake8 src/ tests/

# 型別檢查
mypy src/

# 執行測試
pytest tests/ -v
```

## 📤 提交指南

### 分支命名

使用描述性的分支名稱：

- `feature/add-excel-export` - 新功能
- `bugfix/fix-timezone-conversion` - 錯誤修復
- `docs/update-readme` - 文件更新
- `refactor/improve-parser-performance` - 重構

### 提交訊息

使用清楚的提交訊息格式：

```
類型(範圍): 簡短描述

詳細說明（如有必要）

- 變更項目 1
- 變更項目 2

Fixes #123
```

範例：
```
feat(parser): add support for Excel export

Add functionality to export filtered log data to Excel format.

- Add export_excel method to LogAnalyzer class
- Update GUI with Excel export button
- Add openpyxl dependency for Excel support

Fixes #45
```

### 提交類型

- `feat`: 新功能
- `fix`: 錯誤修復
- `docs`: 文件更新
- `style`: 程式碼格式調整
- `refactor`: 程式碼重構
- `test`: 測試相關
- `chore`: 建置或輔助工具變更

## 🧪 測試指南

### 測試結構

```
tests/
├── unit/                 # 單元測試
│   ├── test_parser.py
│   ├── test_config.py
│   └── test_utils.py
├── integration/          # 整合測試
│   └── test_full_workflow.py
└── conftest.py          # 測試配置
```

### 撰寫測試

```python
import pytest
from exchange_log_analyzer.core.parser import parse_log

class TestLogParser:
    def test_parse_valid_log(self, sample_log_file):
        """Test parsing a valid log file."""
        result = parse_log(sample_log_file)
        
        assert not result.empty
        assert 'date-time' in result.columns
        assert len(result) > 0
    
    def test_parse_invalid_log(self):
        """Test parsing an invalid log file."""
        result = parse_log("nonexistent.log")
        
        assert result.empty
```

### 執行測試

```bash
# 執行所有測試
pytest

# 執行特定測試檔案
pytest tests/unit/test_parser.py

# 執行特定測試類別
pytest tests/unit/test_parser.py::TestLogParser

# 執行特定測試函式
pytest tests/unit/test_parser.py::TestLogParser::test_parse_valid_log

# 顯示測試覆蓋率
pytest --cov=src/exchange_log_analyzer tests/

# 跳過慢速測試
pytest -m "not slow"
```

## 📚 文件撰寫

### 文件類型

- **README.md**: 專案概述和使用指南
- **API 文件**: 函式和類別的詳細說明
- **使用者指南**: 詳細的使用教學
- **開發者指南**: 開發相關的技術文件

### 文件格式

使用 Markdown 格式撰寫文件，並遵循以下結構：

```markdown
# 標題

## 概述

簡短描述功能或概念。

## 使用方法

### 基本用法

```python
# 程式碼範例
```

### 進階用法

詳細說明和範例。

## 參數說明

| 參數 | 類型 | 說明 | 預設值 |
|------|------|------|--------|
| param1 | str | 參數說明 | None |

## 範例

實際使用範例。
```

## 🐛 問題回報

### 回報前檢查

在回報問題前，請先：

1. 搜尋現有的 Issues 確認問題未被回報
2. 確認您使用的是最新版本
3. 檢查是否為已知的限制或預期行為

### 問題回報格式

使用以下模板回報問題：

```markdown
## 問題描述

簡短描述遇到的問題。

## 重現步驟

1. 執行步驟 1
2. 執行步驟 2
3. 觀察到的錯誤

## 預期行為

描述您預期應該發生的行為。

## 實際行為

描述實際發生的行為。

## 環境資訊

- OS: [例如 Windows 10]
- Python 版本: [例如 3.9.0]
- 套件版本: [例如 1.0.9]

## 額外資訊

任何其他相關資訊、錯誤訊息或螢幕截圖。
```

## 💡 功能建議

### 建議格式

```markdown
## 功能描述

清楚描述建議的新功能。

## 使用場景

說明這個功能的使用場景和需求。

## 建議實作

如果有想法，可以描述可能的實作方式。

## 替代方案

是否有其他可能的解決方案。
```

## 📞 聯絡方式

如果您有任何問題或需要協助：

- 📧 Email: [<EMAIL>](mailto:<EMAIL>)
- 🐛 Issues: [GitHub Issues](https://github.com/jackycj0830/ExchangeLogAnalyzer/issues)
- 💬 Discussions: [GitHub Discussions](https://github.com/jackycj0830/ExchangeLogAnalyzer/discussions)

---

感謝您的貢獻！🙏
