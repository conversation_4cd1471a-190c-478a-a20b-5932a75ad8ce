"""
Main window for Exchange Log Analyzer GUI.

This module contains the main application window and its core functionality.
"""

import sys
import os
import datetime
import re
import pytz
import gc
import shutil
import pandas as pd

from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLineEdit,
    QLabel, QDateEdit, QTimeEdit, QFileDialog, QTableWidget, QTableWidgetItem,
    QMessageBox, QMainWindow, QAction, QComboBox, QDialog, QTextEdit,
    QDialogButtonBox, QMenu, QProgressDialog, QInputDialog
)
from PyQt5.QtCore import QDate, QTime, QDateTime, Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

from ..core.parser import LogParser, parse_log, filter_logs
from ..core.config import Config
from ..core.version import VERSION_INFO_HTML
try:
    from .dialogs import SmbDownloadDialog
except ImportError:
    SmbDownloadDialog = None
try:
    from .workers import LogProcessingWorker
except ImportError:
    LogProcessingWorker = None


class LogAnalyzer(QMainWindow):
    """Main application window for Exchange Log Analyzer."""
    
    def __init__(self):
        """Initialize the main window."""
        super().__init__()
        self.config = Config()
        self.parser = LogParser()
        self.current_data = pd.DataFrame()
        self.filtered_data = pd.DataFrame()
        
        self.init_ui()
        self.setup_menu()
        
    def init_ui(self):
        """Initialize the user interface."""
        self.setWindowTitle("Exchange 追蹤日誌分析工具 v1.0.9")
        self.setGeometry(100, 100, self.config.DEFAULT_WINDOW_WIDTH, self.config.DEFAULT_WINDOW_HEIGHT)
        
        # Set window icon if available
        if os.path.exists("svg_344955.ico"):
            self.setWindowIcon(QIcon("svg_344955.ico"))
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Create UI components
        self.create_control_panel(main_layout)
        self.create_data_table(main_layout)
        self.create_status_bar()
        
        # Load version info
        self.load_version_info()
        
    def create_control_panel(self, parent_layout):
        """Create the control panel with search and filter options."""
        # Control panel group
        control_layout = QVBoxLayout()
        
        # File selection row
        file_row = QHBoxLayout()
        self.folder_label = QLabel("選擇 LOG 資料夾:")
        self.folder_path = QLineEdit()
        self.folder_path.setPlaceholderText("請選擇包含 .log 檔案的資料夾")
        self.browse_button = QPushButton("瀏覽資料夾")
        self.browse_button.clicked.connect(self.browse_folder)
        
        file_row.addWidget(self.folder_label)
        file_row.addWidget(self.folder_path)
        file_row.addWidget(self.browse_button)
        
        # Search row
        search_row = QHBoxLayout()
        self.keyword_label = QLabel("關鍵字:")
        self.keyword_input = QLineEdit()
        self.keyword_input.setPlaceholderText("輸入搜尋關鍵字")
        
        search_row.addWidget(self.keyword_label)
        search_row.addWidget(self.keyword_input)
        
        # Date range row
        date_row = QHBoxLayout()
        self.start_date_label = QLabel("開始日期:")
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-7))
        self.start_date.setCalendarPopup(True)
        
        self.start_time_label = QLabel("開始時間:")
        self.start_time = QTimeEdit()
        self.start_time.setTime(QTime(0, 0))
        
        self.end_date_label = QLabel("結束日期:")
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        
        self.end_time_label = QLabel("結束時間:")
        self.end_time = QTimeEdit()
        self.end_time.setTime(QTime(23, 59))
        
        date_row.addWidget(self.start_date_label)
        date_row.addWidget(self.start_date)
        date_row.addWidget(self.start_time_label)
        date_row.addWidget(self.start_time)
        date_row.addWidget(self.end_date_label)
        date_row.addWidget(self.end_date)
        date_row.addWidget(self.end_time_label)
        date_row.addWidget(self.end_time)
        
        # Action buttons row
        button_row = QHBoxLayout()
        self.load_button = QPushButton("載入 LOG 檔案")
        self.load_button.clicked.connect(self.load_logs)
        
        self.search_button = QPushButton("搜尋")
        self.search_button.clicked.connect(self.search_logs)
        
        self.export_button = QPushButton("匯出 CSV")
        self.export_button.clicked.connect(self.export_csv)
        self.export_button.setEnabled(False)
        
        self.clear_button = QPushButton("清除")
        self.clear_button.clicked.connect(self.clear_data)
        
        button_row.addWidget(self.load_button)
        button_row.addWidget(self.search_button)
        button_row.addWidget(self.export_button)
        button_row.addWidget(self.clear_button)
        
        # Add all rows to control layout
        control_layout.addLayout(file_row)
        control_layout.addLayout(search_row)
        control_layout.addLayout(date_row)
        control_layout.addLayout(button_row)
        
        parent_layout.addLayout(control_layout)
        
    def create_data_table(self, parent_layout):
        """Create the data display table."""
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.setSortingEnabled(True)
        parent_layout.addWidget(self.table)
        
    def create_status_bar(self):
        """Create the status bar."""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("就緒")
        
    def setup_menu(self):
        """Setup the application menu bar."""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('檔案')
        
        # SMB download action
        smb_action = QAction('連線至 Exchange 下載 Log', self)
        smb_action.triggered.connect(self.open_smb_dialog)
        file_menu.addAction(smb_action)
        
        file_menu.addSeparator()
        
        # Exit action
        exit_action = QAction('結束', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Help menu
        help_menu = menubar.addMenu('說明')
        
        # About action
        about_action = QAction('關於', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def load_version_info(self):
        """Load and display version information."""
        # This could be displayed in a separate tab or dialog
        pass
        
    def browse_folder(self):
        """Open folder browser dialog."""
        folder = QFileDialog.getExistingDirectory(
            self, "選擇 LOG 資料夾", self.config.log_folder
        )
        if folder:
            self.folder_path.setText(folder)
            
    def load_logs(self):
        """Load log files from the selected folder."""
        folder = self.folder_path.text()
        if not folder:
            QMessageBox.warning(self, "警告", "請先選擇 LOG 資料夾")
            return
            
        if not os.path.exists(folder):
            QMessageBox.warning(self, "警告", "選擇的資料夾不存在")
            return
            
        # Find log files
        log_files = self.config.find_log_files(folder)
        if not log_files:
            QMessageBox.information(self, "資訊", "在選擇的資料夾中找不到 .log 檔案")
            return
            
        # Load logs in background thread
        self.load_logs_async(log_files)
        
    def load_logs_async(self, log_files):
        """Load logs asynchronously."""
        # Create and start worker thread
        self.worker = LogProcessingWorker(log_files, self.parser)
        self.worker.finished.connect(self.on_logs_loaded)
        self.worker.error.connect(self.on_load_error)
        self.worker.start()
        
        # Show progress dialog
        self.progress = QProgressDialog("載入 LOG 檔案中...", "取消", 0, 0, self)
        self.progress.setWindowModality(Qt.WindowModal)
        self.progress.show()
        
    def on_logs_loaded(self, data):
        """Handle successful log loading."""
        self.progress.close()
        self.current_data = data
        self.filtered_data = data.copy()
        self.update_table()
        self.export_button.setEnabled(True)
        self.status_bar.showMessage(f"已載入 {len(data)} 筆記錄")
        
    def on_load_error(self, error_msg):
        """Handle log loading error."""
        self.progress.close()
        QMessageBox.critical(self, "錯誤", f"載入 LOG 檔案時發生錯誤:\n{error_msg}")
        
    def search_logs(self):
        """Search and filter logs based on criteria."""
        if self.current_data.empty:
            QMessageBox.warning(self, "警告", "請先載入 LOG 檔案")
            return
            
        # Get search criteria
        keyword = self.keyword_input.text().strip()
        start_datetime = self.get_start_datetime()
        end_datetime = self.get_end_datetime()
        
        # Filter data
        self.filtered_data = filter_logs(
            self.current_data, keyword, start_datetime, end_datetime
        )
        
        self.update_table()
        self.status_bar.showMessage(f"搜尋結果: {len(self.filtered_data)} 筆記錄")
        
    def get_start_datetime(self):
        """Get start datetime from UI controls."""
        date = self.start_date.date().toPyDate()
        time = self.start_time.time().toPyTime()
        return datetime.datetime.combine(date, time)
        
    def get_end_datetime(self):
        """Get end datetime from UI controls."""
        date = self.end_date.date().toPyDate()
        time = self.end_time.time().toPyTime()
        return datetime.datetime.combine(date, time)
        
    def update_table(self):
        """Update the data table with current filtered data."""
        if self.filtered_data.empty:
            self.table.setRowCount(0)
            self.table.setColumnCount(0)
            return
            
        # Set table dimensions
        self.table.setRowCount(len(self.filtered_data))
        self.table.setColumnCount(len(self.filtered_data.columns))
        self.table.setHorizontalHeaderLabels(self.filtered_data.columns.tolist())
        
        # Populate table
        for i, row in self.filtered_data.iterrows():
            for j, value in enumerate(row):
                item = QTableWidgetItem(str(value))
                self.table.setItem(i, j, item)
                
        # Auto-resize columns
        self.table.resizeColumnsToContents()
        
    def export_csv(self):
        """Export filtered data to CSV file."""
        if self.filtered_data.empty:
            QMessageBox.warning(self, "警告", "沒有資料可以匯出")
            return
            
        filename, _ = QFileDialog.getSaveFileName(
            self, "匯出 CSV", "exchange_logs.csv", "CSV files (*.csv)"
        )
        
        if filename:
            try:
                self.filtered_data.to_csv(filename, index=False, encoding='utf-8-sig')
                QMessageBox.information(self, "成功", f"資料已匯出至:\n{filename}")
            except Exception as e:
                QMessageBox.critical(self, "錯誤", f"匯出失敗:\n{str(e)}")
                
    def clear_data(self):
        """Clear all data and reset the interface."""
        self.current_data = pd.DataFrame()
        self.filtered_data = pd.DataFrame()
        self.table.setRowCount(0)
        self.table.setColumnCount(0)
        self.export_button.setEnabled(False)
        self.status_bar.showMessage("已清除資料")
        
    def open_smb_dialog(self):
        """Open SMB download dialog."""
        dialog = SmbDownloadDialog(self)
        dialog.exec_()
        
    def show_about(self):
        """Show about dialog."""
        about_dialog = QDialog(self)
        about_dialog.setWindowTitle("關於")
        about_dialog.setMinimumSize(800, 600)
        
        layout = QVBoxLayout(about_dialog)
        
        text_edit = QTextEdit()
        text_edit.setHtml(VERSION_INFO_HTML)
        text_edit.setReadOnly(True)
        layout.addWidget(text_edit)
        
        button_box = QDialogButtonBox(QDialogButtonBox.Ok)
        button_box.accepted.connect(about_dialog.accept)
        layout.addWidget(button_box)
        
        about_dialog.exec_()
