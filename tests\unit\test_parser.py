"""
Unit tests for the parser module.

This module contains tests for the log parsing functionality.
"""

import pytest
import pandas as pd
from datetime import datetime
import tempfile
import os

from exchange_log_analyzer.core.parser import LogParser, parse_log, filter_logs


class TestLogParser:
    """Test cases for LogParser class."""
    
    def test_init_default_timezone(self):
        """Test LogParser initialization with default timezone."""
        parser = LogParser()
        assert parser.timezone == 'UTC'
    
    def test_init_custom_timezone(self):
        """Test LogParser initialization with custom timezone."""
        parser = LogParser(timezone='Asia/Taipei')
        assert parser.timezone == 'Asia/Taipei'
    
    def test_parse_file_success(self, log_parser, sample_log_file):
        """Test successful parsing of a log file."""
        result = log_parser.parse_file(sample_log_file)
        
        assert isinstance(result, pd.DataFrame)
        assert not result.empty
        assert len(result) == 2  # Two log entries in sample
        assert 'date-time' in result.columns
        assert 'event-id' in result.columns
        assert 'recipient-address' in result.columns
    
    def test_parse_file_nonexistent(self, log_parser):
        """Test parsing of non-existent file."""
        result = log_parser.parse_file("nonexistent.log")
        
        assert isinstance(result, pd.DataFrame)
        assert result.empty
    
    def test_filter_data_keyword(self, log_parser, sample_dataframe):
        """Test filtering data by keyword."""
        result = log_parser.filter_data(sample_dataframe, keyword="RECEIVE")
        
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 1
        assert result.iloc[0]['event-id'] == 'RECEIVE'
    
    def test_filter_data_date_range(self, log_parser, sample_dataframe):
        """Test filtering data by date range."""
        start_date = "2025-01-20 08:30:15"
        end_date = "2025-01-20 08:30:15"
        
        result = log_parser.filter_data(
            sample_dataframe, 
            start_date=start_date, 
            end_date=end_date
        )
        
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 1


class TestParseLogFunction:
    """Test cases for parse_log function."""
    
    def test_parse_log_success(self, sample_log_file):
        """Test successful log parsing."""
        result = parse_log(sample_log_file)
        
        assert isinstance(result, pd.DataFrame)
        assert not result.empty
        assert 'date-time' in result.columns
        
        # Check datetime conversion
        assert pd.api.types.is_datetime64_any_dtype(result['date-time'])
    
    def test_parse_log_with_timezone(self, sample_log_file):
        """Test log parsing with timezone conversion."""
        result = parse_log(sample_log_file, timezone='Asia/Taipei')
        
        assert isinstance(result, pd.DataFrame)
        assert not result.empty
        
        # Check that timezone is applied
        if not result.empty and 'date-time' in result.columns:
            dt_col = result['date-time'].dropna()
            if not dt_col.empty:
                assert dt_col.iloc[0].tz is not None
    
    def test_parse_log_invalid_file(self, invalid_log_file):
        """Test parsing of invalid log file."""
        result = parse_log(invalid_log_file)
        
        # Should return empty DataFrame for invalid files
        assert isinstance(result, pd.DataFrame)
        assert result.empty
    
    def test_parse_log_empty_file(self, empty_log_file):
        """Test parsing of empty log file."""
        result = parse_log(empty_log_file)
        
        assert isinstance(result, pd.DataFrame)
        assert result.empty


class TestFilterLogsFunction:
    """Test cases for filter_logs function."""
    
    def test_filter_logs_no_filters(self, sample_dataframe):
        """Test filtering with no filters applied."""
        result = filter_logs(sample_dataframe)
        
        assert isinstance(result, pd.DataFrame)
        assert len(result) == len(sample_dataframe)
        pd.testing.assert_frame_equal(result, sample_dataframe)
    
    def test_filter_logs_keyword_case_insensitive(self, sample_dataframe):
        """Test keyword filtering is case insensitive."""
        result = filter_logs(sample_dataframe, keyword="receive")
        
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 1
        assert result.iloc[0]['event-id'] == 'RECEIVE'
    
    def test_filter_logs_keyword_multiple_columns(self, sample_dataframe):
        """Test keyword filtering across multiple columns."""
        result = filter_logs(sample_dataframe, keyword="example.com")
        
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 2  # Should match both rows (sender-address)
    
    def test_filter_logs_keyword_no_match(self, sample_dataframe):
        """Test keyword filtering with no matches."""
        result = filter_logs(sample_dataframe, keyword="nonexistent")
        
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 0
    
    def test_filter_logs_start_date(self, sample_dataframe):
        """Test filtering by start date."""
        start_date = "2025-01-20 08:30:16"
        result = filter_logs(sample_dataframe, start_date=start_date)
        
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 1
        assert result.iloc[0]['event-id'] == 'SEND'
    
    def test_filter_logs_end_date(self, sample_dataframe):
        """Test filtering by end date."""
        end_date = "2025-01-20 08:30:15"
        result = filter_logs(sample_dataframe, end_date=end_date)
        
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 1
        assert result.iloc[0]['event-id'] == 'RECEIVE'
    
    def test_filter_logs_date_range(self, sample_dataframe):
        """Test filtering by date range."""
        start_date = "2025-01-20 08:30:15"
        end_date = "2025-01-20 08:30:16"
        result = filter_logs(
            sample_dataframe, 
            start_date=start_date, 
            end_date=end_date
        )
        
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 2
    
    def test_filter_logs_combined_filters(self, sample_dataframe):
        """Test filtering with combined keyword and date filters."""
        result = filter_logs(
            sample_dataframe,
            keyword="SEND",
            start_date="2025-01-20 08:30:16",
            end_date="2025-01-20 08:30:16"
        )
        
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 1
        assert result.iloc[0]['event-id'] == 'SEND'
    
    def test_filter_logs_empty_dataframe(self):
        """Test filtering empty DataFrame."""
        empty_df = pd.DataFrame()
        result = filter_logs(empty_df, keyword="test")
        
        assert isinstance(result, pd.DataFrame)
        assert result.empty
    
    def test_filter_logs_dataframe_without_datetime(self):
        """Test filtering DataFrame without date-time column."""
        df = pd.DataFrame({
            'event-id': ['RECEIVE', 'SEND'],
            'message': ['test1', 'test2']
        })
        
        result = filter_logs(df, keyword="RECEIVE")
        
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 1
        assert result.iloc[0]['event-id'] == 'RECEIVE'


@pytest.mark.slow
class TestParserPerformance:
    """Performance tests for parser functions."""
    
    def test_parse_large_file_performance(self, tmp_path):
        """Test parsing performance with larger files."""
        # Create a larger test file
        large_content = """#Software: Microsoft Exchange Server
#Version: 15.01.2507.012
#Log-type: Message Tracking Log
#Date: 2025-01-20T00:00:00.000Z
#Fields: date-time,event-id,recipient-address,sender-address,message-subject
"""
        
        # Add many log entries
        for i in range(1000):
            large_content += f"2025-01-20T08:{i%60:02d}:00.000Z,RECEIVE,user{i}@domain.com,<EMAIL>,Test Subject {i}\n"
        
        large_file = tmp_path / "large.log"
        large_file.write_text(large_content, encoding='utf-8')
        
        # Test parsing
        import time
        start_time = time.time()
        result = parse_log(str(large_file))
        end_time = time.time()
        
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 1000
        assert (end_time - start_time) < 10  # Should complete within 10 seconds
