"""
Unit tests for the config module.

This module contains tests for configuration management functionality.
"""

import pytest
import os
import tempfile
from pathlib import Path

from exchange_log_analyzer.core.config import Config, find_log_files


class TestConfig:
    """Test cases for Config class."""
    
    def test_init_default_log_folder(self):
        """Test Config initialization with default log folder."""
        config = Config()
        assert config.log_folder == Config.DEFAULT_LOG_FOLDER
    
    def test_init_custom_log_folder(self):
        """Test Config initialization with custom log folder."""
        custom_path = "/custom/path"
        config = Config(log_folder=custom_path)
        assert config.log_folder == custom_path
    
    def test_find_log_files_existing_folder(self, temp_directory):
        """Test finding log files in existing folder."""
        config = Config()
        
        # Create test log files
        test_files = ['test1.log', 'test2.log', 'test.txt']
        for filename in test_files:
            file_path = os.path.join(temp_directory, filename)
            with open(file_path, 'w') as f:
                f.write("test content")
        
        result = config.find_log_files(temp_directory)
        
        assert isinstance(result, list)
        assert len(result) == 2  # Only .log files
        assert all(f.endswith('.log') for f in result)
        assert all(os.path.exists(f) for f in result)
    
    def test_find_log_files_nonexistent_folder(self):
        """Test finding log files in non-existent folder."""
        config = Config()
        result = config.find_log_files("/nonexistent/path")
        
        assert isinstance(result, list)
        assert len(result) == 0
    
    def test_find_log_files_empty_folder(self, temp_directory):
        """Test finding log files in empty folder."""
        config = Config()
        result = config.find_log_files(temp_directory)
        
        assert isinstance(result, list)
        assert len(result) == 0
    
    def test_find_log_files_no_log_files(self, temp_directory):
        """Test finding log files when no .log files exist."""
        config = Config()
        
        # Create non-log files
        test_files = ['test.txt', 'data.csv', 'config.ini']
        for filename in test_files:
            file_path = os.path.join(temp_directory, filename)
            with open(file_path, 'w') as f:
                f.write("test content")
        
        result = config.find_log_files(temp_directory)
        
        assert isinstance(result, list)
        assert len(result) == 0
    
    def test_find_log_files_default_folder(self):
        """Test finding log files using default folder."""
        config = Config()
        result = config.find_log_files()
        
        # Should use the configured log folder
        assert isinstance(result, list)
        # Result may be empty if default path doesn't exist, which is fine
    
    def test_find_log_files_sorted_output(self, temp_directory):
        """Test that log files are returned in sorted order."""
        config = Config()
        
        # Create log files with names that would be unsorted
        test_files = ['z_test.log', 'a_test.log', 'm_test.log']
        for filename in test_files:
            file_path = os.path.join(temp_directory, filename)
            with open(file_path, 'w') as f:
                f.write("test content")
        
        result = config.find_log_files(temp_directory)
        
        assert isinstance(result, list)
        assert len(result) == 3
        
        # Extract filenames and check if sorted
        filenames = [os.path.basename(f) for f in result]
        assert filenames == sorted(filenames)
    
    def test_validate_log_folder_existing(self, temp_directory):
        """Test validating existing folder."""
        config = Config()
        assert config.validate_log_folder(temp_directory) is True
    
    def test_validate_log_folder_nonexistent(self):
        """Test validating non-existent folder."""
        config = Config()
        assert config.validate_log_folder("/nonexistent/path") is False
    
    def test_validate_log_folder_file_not_directory(self, temp_directory):
        """Test validating a file path instead of directory."""
        config = Config()
        
        # Create a file
        file_path = os.path.join(temp_directory, "test.txt")
        with open(file_path, 'w') as f:
            f.write("test")
        
        assert config.validate_log_folder(file_path) is False
    
    def test_validate_log_folder_none(self):
        """Test validating None path."""
        config = Config()
        assert config.validate_log_folder(None) is False
    
    def test_validate_log_folder_empty_string(self):
        """Test validating empty string path."""
        config = Config()
        assert config.validate_log_folder("") is False


class TestConfigConstants:
    """Test cases for Config class constants."""
    
    def test_default_log_folder_constant(self):
        """Test that default log folder constant is set."""
        assert hasattr(Config, 'DEFAULT_LOG_FOLDER')
        assert isinstance(Config.DEFAULT_LOG_FOLDER, str)
        assert len(Config.DEFAULT_LOG_FOLDER) > 0
    
    def test_supported_extensions_constant(self):
        """Test that supported extensions constant is set."""
        assert hasattr(Config, 'SUPPORTED_EXTENSIONS')
        assert isinstance(Config.SUPPORTED_EXTENSIONS, list)
        assert '.log' in Config.SUPPORTED_EXTENSIONS
    
    def test_default_timezone_constant(self):
        """Test that default timezone constant is set."""
        assert hasattr(Config, 'DEFAULT_TIMEZONE')
        assert Config.DEFAULT_TIMEZONE == 'UTC'
    
    def test_gui_settings_constants(self):
        """Test that GUI settings constants are set."""
        assert hasattr(Config, 'DEFAULT_WINDOW_WIDTH')
        assert hasattr(Config, 'DEFAULT_WINDOW_HEIGHT')
        assert isinstance(Config.DEFAULT_WINDOW_WIDTH, int)
        assert isinstance(Config.DEFAULT_WINDOW_HEIGHT, int)
        assert Config.DEFAULT_WINDOW_WIDTH > 0
        assert Config.DEFAULT_WINDOW_HEIGHT > 0


class TestFindLogFilesFunction:
    """Test cases for find_log_files convenience function."""
    
    def test_find_log_files_function_with_path(self, temp_directory):
        """Test find_log_files function with specific path."""
        # Create test log files
        test_files = ['test1.log', 'test2.log']
        for filename in test_files:
            file_path = os.path.join(temp_directory, filename)
            with open(file_path, 'w') as f:
                f.write("test content")
        
        result = find_log_files(temp_directory)
        
        assert isinstance(result, list)
        assert len(result) == 2
        assert all(f.endswith('.log') for f in result)
    
    def test_find_log_files_function_without_path(self):
        """Test find_log_files function without path (uses default)."""
        result = find_log_files()
        
        assert isinstance(result, list)
        # Result may be empty if default path doesn't exist
    
    def test_find_log_files_function_nonexistent_path(self):
        """Test find_log_files function with non-existent path."""
        result = find_log_files("/nonexistent/path")
        
        assert isinstance(result, list)
        assert len(result) == 0


class TestConfigIntegration:
    """Integration tests for Config class."""
    
    def test_config_workflow(self, temp_directory):
        """Test complete workflow with Config class."""
        # Initialize config
        config = Config(log_folder=temp_directory)
        
        # Validate folder
        assert config.validate_log_folder(temp_directory) is True
        
        # Create log files
        log_files = ['exchange1.log', 'exchange2.log', 'other.txt']
        for filename in log_files:
            file_path = os.path.join(temp_directory, filename)
            with open(file_path, 'w') as f:
                f.write("#Software: Microsoft Exchange Server\n")
                f.write("#Fields: date-time,event-id\n")
                f.write("2025-01-20T08:30:00.000Z,RECEIVE\n")
        
        # Find log files
        found_files = config.find_log_files()
        
        assert len(found_files) == 2  # Only .log files
        assert all('exchange' in os.path.basename(f) for f in found_files)
        assert all(f.endswith('.log') for f in found_files)
    
    def test_config_with_subdirectories(self, temp_directory):
        """Test config behavior with subdirectories."""
        config = Config(log_folder=temp_directory)
        
        # Create subdirectory with log files
        subdir = os.path.join(temp_directory, "subdir")
        os.makedirs(subdir)
        
        # Create log files in main directory
        main_log = os.path.join(temp_directory, "main.log")
        with open(main_log, 'w') as f:
            f.write("main log content")
        
        # Create log files in subdirectory
        sub_log = os.path.join(subdir, "sub.log")
        with open(sub_log, 'w') as f:
            f.write("sub log content")
        
        # find_log_files should only find files in the main directory
        found_files = config.find_log_files()
        
        assert len(found_files) == 1
        assert os.path.basename(found_files[0]) == "main.log"
